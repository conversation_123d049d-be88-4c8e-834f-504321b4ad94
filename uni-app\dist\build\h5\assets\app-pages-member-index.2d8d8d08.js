import{_ as o}from"./loading-page.vue_vue_type_script_setup_true_lang.1c76425e.js";import{d as t,p as e,l as s,u as r,r as a,a as p,o as i,c as u,w as m,b as n,e as _,f as l,v as j,g as d,n as g,i as c,j as f,k as v}from"./index-12d8dd28.js";import{u as y}from"./useDiy.79a9de97.js";import{d as b}from"./index.7dbc3a0e.js";import{_ as x}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-loading-icon.85553345.js";import"./u-transition.5e23e77b.js";/* empty css                                                                     */import"./u-icon.52b2e8b4.js";/* empty css                                                               */import"./u-popup.155f7cb9.js";import"./u-safe-bottom.a6f7ba25.js";import"./top-tabbar.d86f4f04.js";import"./manifest.ed582bbb.js";import"./area-select.vue_vue_type_script_setup_true_lang.ac35f8f0.js";import"./u-checkbox.cf8c867e.js";import"./u-checkbox-group.aeb040c3.js";import"./u-button.82ece83a.js";import"./u-input.52919f31.js";import"./u-picker.00677d8c.js";import"./u-upload.837ab45e.js";import"./u-radio-group.2222b472.js";import"./diy_form.03332942.js";import"./u-action-sheet.7a6074b9.js";import"./u-line.10a9c4d6.js";import"./u-avatar.47324b30.js";import"./u-text.40aa3da3.js";import"./u-parse.62eaef17.js";import"./tabbar.7121cec5.js";import"./u-badge.19b04045.js";import"./u-tabbar.82e98c18.js";import"./category.ec941c14.js";import"./common.eabc72c7.js";import"./project.82b2754b.js";import"./index.73e4261f.js";import"./u--image.376d9df2.js";import"./u-image.f86d3186.js";/* empty css                                                                */import"./goods.a1ad401e.js";import"./useGoods.854430c9.js";import"./add-cart-popup.b39c6ac4.js";import"./u-number-box.51df5baa.js";import"./coupon.dc9a77f8.js";import"./point.b4ad8bb4.js";import"./rank.4f838a63.js";import"./bind-mobile.0e3d01ed.js";import"./u-form.b43211a9.js";import"./sms-code.vue_vue_type_script_setup_true_lang.bd6d03c4.js";import"./u-modal.603e6547.js";import"./newcomer.1c902485.js";import"./order.5fba8f66.js";const h=x(t({__name:"index",setup(t){const x=e(),h=s((()=>x.info)),{setShare:k}=r(),S=y({name:"DIY_MEMBER_INDEX"}),w=a(null);return a(null),S.onLoad(),S.onShow((o=>{var t;o.value||o.page&&p({url:o.page,mode:"reLaunch"});let s=o.share?JSON.parse(o.share):null;k(s),null==(t=w.value)||t.refresh(),h.value&&e().getMemberInfo()})),S.onHide(),S.onUnload(),S.onPageScroll(),(t,e)=>{const s=c(f("loading-page"),o),r=v;return i(),u(r,{style:g(t.themeColor())},{default:m((()=>[n(s,{loading:_(S).getLoading()},null,8,["loading"]),l(n(r,null,{default:m((()=>[d(" 自定义模板渲染 "),n(r,{class:"diy-template-wrap bg-index",style:g(_(S).pageStyle())},{default:m((()=>[n(b,{ref_key:"diyGroupRef",ref:w,data:_(S).data},null,8,["data"])])),_:1},8,["style"])])),_:1},512),[[j,!_(S).getLoading()]])])),_:1},8,["style"])}}}),[["__scopeId","data-v-93171cda"]]);export{h as default};
