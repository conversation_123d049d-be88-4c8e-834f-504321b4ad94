import{d as e,r as o,o as t,c as l,w as s,b as n,e as a,A as r,g as c,G as d,I as m,k as i,E as p}from"./index-12d8dd28.js";import{d as u}from"./index.73e4261f.js";const x=e({__name:"ns-goods-recommend",setup(e){const x=o(),f=e=>{x.value=e||{}},g=o({style:"style-2",num:10,source:"all",topElementRounded:12,bottomElementRounded:12,margin:{both:10,bottom:0,top:0},priceStyle:{mainColor:"#FF4142",control:!0},goodsNameStyle:{color:"#303133",control:!0,fontWeight:"normal"},saleStyle:{color:"#999",control:!0},labelStyle:{isShow:!0,control:!0},btnStyle:{fontWeight:!1,padding:0,aroundRadius:25,textColor:"#fff",startBgColor:"#FF4142",endBgColor:"#FF4142",style:"nc-icon-gouwuche1",control:!0,cartEvent:"cart"}});return(e,o)=>{const h=d,_=m,y=i;return t(),l(y,{class:"goods-recommend"},{default:s((()=>[n(y,{class:"mt-[60rpx] flex flex-col items-center sidebar-margin"},{default:s((()=>[x.value&&Object.keys(x.value).length?(t(),l(y,{key:0,class:"flex items-center mb-[30rpx]"},{default:s((()=>[n(h,{class:"w-[38rpx] h-[22rpx]",src:a(p)("addon/shop_fenxiao/level/title_left.png"),mode:"aspectFill"},null,8,["src"]),n(_,{class:"text-[30rpx] mx-[18rpx] font-500 text-[#EF000C]"},{default:s((()=>[r("猜你喜欢")])),_:1}),n(h,{class:"w-[38rpx] h-[22rpx]",src:a(p)("addon/shop_fenxiao/level/title_right.png"),mode:"aspectFill"},null,8,["src"])])),_:1})):c("v-if",!0),n(u,{onLoadingFn:f,component:g.value},null,8,["component"])])),_:1}),n(y,{class:"h-[50rpx]"})])),_:1})}}});export{x as _};
