import{a4 as e,a5 as t,a6 as o,a8 as a,a7 as i,bd as l,bl as n,X as r,i as s,j as c,o as u,c as d,w as m,n as f,g as h,b as p,T as g,$ as y,A as _,B as b,k as x,I as S,bc as k,bg as v,ay as C,aN as j,R as w,S as B,a3 as I,aH as P,aI as N,aK as $,ao as D,bj as T,aw as z,G as V}from"./index-12d8dd28.js";import{b as F}from"./u-popup.155f7cb9.js";import{_ as U}from"./u-icon.52b2e8b4.js";import{_ as H}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as K}from"./u-picker.00677d8c.js";import{_ as E}from"./u-input.52919f31.js";import{_ as A,a as O}from"./u-radio-group.2222b472.js";import{_ as L}from"./u-button.82ece83a.js";import{l as q}from"./project.82b2754b.js";import"./u-transition.5e23e77b.js";/* empty css                                                                     */import"./u-safe-bottom.a6f7ba25.js";/* empty css                                                               */import"./u-loading-icon.85553345.js";const R=H({name:"u-navbar",mixins:[t,o,{props:{safeAreaInsetTop:{type:Boolean,default:()=>e.navbar.safeAreaInsetTop},placeholder:{type:Boolean,default:()=>e.navbar.placeholder},fixed:{type:Boolean,default:()=>e.navbar.fixed},border:{type:Boolean,default:()=>e.navbar.border},leftIcon:{type:String,default:()=>e.navbar.leftIcon},leftText:{type:String,default:()=>e.navbar.leftText},rightText:{type:String,default:()=>e.navbar.rightText},rightIcon:{type:String,default:()=>e.navbar.rightIcon},title:{type:[String,Number],default:()=>e.navbar.title},bgColor:{type:String,default:()=>e.navbar.bgColor},titleWidth:{type:[String,Number],default:()=>e.navbar.titleWidth},height:{type:[String,Number],default:()=>e.navbar.height},leftIconSize:{type:[String,Number],default:()=>e.navbar.leftIconSize},leftIconColor:{type:String,default:()=>e.navbar.leftIconColor},autoBack:{type:Boolean,default:()=>e.navbar.autoBack},titleStyle:{type:[String,Object],default:()=>e.navbar.titleStyle}}}],data:()=>({}),emits:["leftClick","rightClick"],methods:{addStyle:a,addUnit:i,sys:l,getPx:n,leftClick(){this.$emit("leftClick"),this.autoBack&&r()},rightClick(){this.$emit("rightClick")}}},[["render",function(e,t,o,a,i,l){const n=x,r=s(c("u-status-bar"),F),k=s(c("u-icon"),U),v=S;return u(),d(n,{class:"u-navbar"},{default:m((()=>[e.fixed&&e.placeholder?(u(),d(n,{key:0,class:"u-navbar__placeholder",style:f({height:l.addUnit(l.getPx(e.height)+l.sys().statusBarHeight,"px")})},null,8,["style"])):h("v-if",!0),p(n,{class:g([e.fixed&&"u-navbar--fixed"])},{default:m((()=>[e.safeAreaInsetTop?(u(),d(r,{key:0,bgColor:e.bgColor},null,8,["bgColor"])):h("v-if",!0),p(n,{class:g(["u-navbar__content",[e.border&&"u-border-bottom"]]),style:f({height:l.addUnit(e.height),backgroundColor:e.bgColor})},{default:m((()=>[p(n,{class:"u-navbar__content__left","hover-class":"u-navbar__content__left--hover","hover-start-time":"150",onClick:l.leftClick},{default:m((()=>[y(e.$slots,"left",{},(()=>[e.leftIcon?(u(),d(k,{key:0,name:e.leftIcon,size:e.leftIconSize,color:e.leftIconColor},null,8,["name","size","color"])):h("v-if",!0),e.leftText?(u(),d(v,{key:1,style:f({color:e.leftIconColor}),class:"u-navbar__content__left__text"},{default:m((()=>[_(b(e.leftText),1)])),_:1},8,["style"])):h("v-if",!0)]),!0)])),_:3},8,["onClick"]),y(e.$slots,"center",{},(()=>[p(v,{class:"u-line-1 u-navbar__content__title",style:f([{width:l.addUnit(e.titleWidth)},l.addStyle(e.titleStyle)])},{default:m((()=>[_(b(e.title),1)])),_:1},8,["style"])]),!0),e.$slots.right||e.rightIcon||e.rightText?(u(),d(n,{key:0,class:"u-navbar__content__right",onClick:l.rightClick},{default:m((()=>[y(e.$slots,"right",{},(()=>[e.rightIcon?(u(),d(k,{key:0,name:e.rightIcon,size:"20"},null,8,["name"])):h("v-if",!0),e.rightText?(u(),d(v,{key:1,class:"u-navbar__content__right__text"},{default:m((()=>[_(b(e.rightText),1)])),_:1})):h("v-if",!0)]),!0)])),_:3},8,["onClick"])):h("v-if",!0)])),_:3},8,["class","style"])])),_:3},8,["class"])])),_:3})}],["__scopeId","data-v-488ed387"]]);const G=H({name:"u-textarea",mixins:[t,o,{props:{value:{type:[String,Number],default:()=>e.textarea.value},modelValue:{type:[String,Number],default:()=>e.textarea.value},placeholder:{type:[String,Number],default:()=>e.textarea.placeholder},placeholderClass:{type:String,default:()=>e.input.placeholderClass},placeholderStyle:{type:[String,Object],default:()=>e.input.placeholderStyle},height:{type:[String,Number],default:()=>e.textarea.height},confirmType:{type:String,default:()=>e.textarea.confirmType},disabled:{type:Boolean,default:()=>e.textarea.disabled},count:{type:Boolean,default:()=>e.textarea.count},focus:{type:Boolean,default:()=>e.textarea.focus},autoHeight:{type:Boolean,default:()=>e.textarea.autoHeight},fixed:{type:Boolean,default:()=>e.textarea.fixed},cursorSpacing:{type:Number,default:()=>e.textarea.cursorSpacing},cursor:{type:[String,Number],default:()=>e.textarea.cursor},showConfirmBar:{type:Boolean,default:()=>e.textarea.showConfirmBar},selectionStart:{type:Number,default:()=>e.textarea.selectionStart},selectionEnd:{type:Number,default:()=>e.textarea.selectionEnd},adjustPosition:{type:Boolean,default:()=>e.textarea.adjustPosition},disableDefaultPadding:{type:Boolean,default:()=>e.textarea.disableDefaultPadding},holdKeyboard:{type:Boolean,default:()=>e.textarea.holdKeyboard},maxlength:{type:[String,Number],default:()=>e.textarea.maxlength},border:{type:String,default:()=>e.textarea.border},formatter:{type:[Function,null],default:()=>e.textarea.formatter},ignoreCompositionEvent:{type:Boolean,default:!0}}}],data:()=>({innerValue:"",focused:!1,firstChange:!0,changeFromInner:!1,innerFormatter:e=>e}),created(){},watch:{modelValue:{immediate:!0,handler(e,t){this.innerValue=e,!1===this.firstChange&&!1===this.changeFromInner&&this.valueChange(),this.firstChange=!1,this.changeFromInner=!1}}},computed:{textareaClass(){let e=[],{border:t,disabled:o}=this;return"surround"===t&&(e=e.concat(["u-border","u-textarea--radius"])),"bottom"===t&&(e=e.concat(["u-border-bottom","u-textarea--no-radius"])),o&&e.push("u-textarea--disabled"),e.join(" ")},textareaStyle(){return k({},a(this.customStyle))}},emits:["update:modelValue","linechange","focus","blur","change","confirm","keyboardheightchange"],methods:{addStyle:a,addUnit:i,setFormatter(e){this.innerFormatter=e},onFocus(e){this.$emit("focus",e)},onBlur(e){this.$emit("blur",e),v(this,"blur")},onLinechange(e){this.$emit("linechange",e)},onInput(e){let{value:t=""}=e.detail||{};const o=(this.formatter||this.innerFormatter)(t);this.innerValue=t,this.$nextTick((()=>{this.innerValue=o,this.valueChange()}))},valueChange(){const e=this.innerValue;this.$nextTick((()=>{this.$emit("update:modelValue",e),this.changeFromInner=!0,this.$emit("change",e),v(this,"change")}))},onConfirm(e){this.$emit("confirm",e)},onKeyboardheightchange(e){this.$emit("keyboardheightchange",e)}}},[["render",function(e,t,o,a,i,l){const n=C,r=S,s=x;return u(),d(s,{class:g(["u-textarea",l.textareaClass]),style:f([l.textareaStyle])},{default:m((()=>[p(n,{class:"u-textarea__field",value:i.innerValue,style:f({height:l.addUnit(e.height)}),placeholder:e.placeholder,"placeholder-style":l.addStyle(e.placeholderStyle,"string"),"placeholder-class":e.placeholderClass,disabled:e.disabled,focus:e.focus,autoHeight:e.autoHeight,fixed:e.fixed,cursorSpacing:e.cursorSpacing,cursor:e.cursor,showConfirmBar:e.showConfirmBar,selectionStart:e.selectionStart,selectionEnd:e.selectionEnd,adjustPosition:e.adjustPosition,disableDefaultPadding:e.disableDefaultPadding,holdKeyboard:e.holdKeyboard,maxlength:e.maxlength,"confirm-type":e.confirmType,ignoreCompositionEvent:e.ignoreCompositionEvent,onFocus:l.onFocus,onBlur:l.onBlur,onLinechange:l.onLinechange,onInput:l.onInput,onConfirm:l.onConfirm,onKeyboardheightchange:l.onKeyboardheightchange},null,8,["value","style","placeholder","placeholder-style","placeholder-class","disabled","focus","autoHeight","fixed","cursorSpacing","cursor","showConfirmBar","selectionStart","selectionEnd","adjustPosition","disableDefaultPadding","holdKeyboard","maxlength","confirm-type","ignoreCompositionEvent","onFocus","onBlur","onLinechange","onInput","onConfirm","onKeyboardheightchange"]),e.count?(u(),d(r,{key:0,class:"u-textarea__count",style:f({"background-color":e.disabled?"transparent":"#fff"})},{default:m((()=>[_(b(i.innerValue.length)+"/"+b(e.maxlength),1)])),_:1},8,["style"])):h("v-if",!0)])),_:1},8,["class","style"])}],["__scopeId","data-v-56565a8a"]]);const M=H({mixins:[t,o,{props:{text:{type:[Array],default:()=>e.columnNotice.text},icon:{type:String,default:()=>e.columnNotice.icon},mode:{type:String,default:()=>e.columnNotice.mode},color:{type:String,default:()=>e.columnNotice.color},bgColor:{type:String,default:()=>e.columnNotice.bgColor},fontSize:{type:[String,Number],default:()=>e.columnNotice.fontSize},speed:{type:[String,Number],default:()=>e.columnNotice.speed},step:{type:Boolean,default:()=>e.columnNotice.step},duration:{type:[String,Number],default:()=>e.columnNotice.duration},disableTouch:{type:Boolean,default:()=>e.columnNotice.disableTouch}}}],watch:{text:{immediate:!0,handler(e,t){j.array(e)}}},computed:{textStyle(){let e={};return e.color=this.color,e.fontSize=i(this.fontSize),e},vertical(){return"horizontal"!=this.mode}},data:()=>({index:0}),emits:["click","close"],methods:{noticeChange(e){this.index=e.detail.current},clickHandler(){this.$emit("click",this.index)},close(){this.$emit("close")}}},[["render",function(e,t,o,a,i,l){const n=s(c("u-icon"),U),r=x,g=S,k=P,v=N;return u(),d(r,{class:"u-notice",onClick:l.clickHandler},{default:m((()=>[y(e.$slots,"icon",{},(()=>[e.icon?(u(),d(r,{key:0,class:"u-notice__left-icon"},{default:m((()=>[p(n,{name:e.icon,color:e.color,size:"19"},null,8,["name","color"])])),_:1})):h("v-if",!0)]),!0),p(v,{"disable-touch":e.disableTouch,vertical:!e.step,circular:"",interval:e.duration,autoplay:!0,class:"u-notice__swiper",onChange:l.noticeChange},{default:m((()=>[(u(!0),w(B,null,I(e.text,((e,t)=>(u(),d(k,{key:t,class:"u-notice__swiper__item"},{default:m((()=>[p(g,{class:"u-notice__swiper__item__text u-line-1",style:f([l.textStyle])},{default:m((()=>[_(b(e),1)])),_:2},1032,["style"])])),_:2},1024)))),128))])),_:1},8,["disable-touch","vertical","interval","onChange"]),["link","closable"].includes(e.mode)?(u(),d(r,{key:0,class:"u-notice__right-icon"},{default:m((()=>["link"===e.mode?(u(),d(n,{key:0,name:"arrow-right",size:17,color:e.color},null,8,["color"])):h("v-if",!0),"closable"===e.mode?(u(),d(n,{key:1,name:"close",size:16,color:e.color,onClick:l.close},null,8,["color","onClick"])):h("v-if",!0)])),_:1})):h("v-if",!0)])),_:3},8,["onClick"])}],["__scopeId","data-v-8f23e910"]]);const W=H({name:"u-row-notice",mixins:[t,o,{props:{text:{type:String,default:()=>e.rowNotice.text},icon:{type:String,default:()=>e.rowNotice.icon},mode:{type:String,default:()=>e.rowNotice.mode},color:{type:String,default:()=>e.rowNotice.color},bgColor:{type:String,default:()=>e.rowNotice.bgColor},fontSize:{type:[String,Number],default:()=>e.rowNotice.fontSize},speed:{type:[String,Number],default:()=>e.rowNotice.speed}}}],data:()=>({animationDuration:"0",animationPlayState:"paused",nvueInit:!0,show:!0}),watch:{text:{immediate:!0,handler(e,t){this.vue(),j.string(e)}},fontSize(){this.vue()},speed(){this.vue()}},computed:{textStyle(){let e={whiteSpace:"nowrap !important"};return e.color=this.color,e.fontSize=i(this.fontSize),e},animationStyle(){let e={};return e.animationDuration=this.animationDuration,e.animationPlayState=this.animationPlayState,e},innerText(){let e=[];const t=this.text.split("");for(let o=0;o<t.length;o+=20)e.push(t.slice(o,o+20).join(""));return e}},mounted(){this.init()},emits:["click","close"],methods:{init(){this.vue(),j.string(this.text)},async vue(){let e=0;await $(),e=(await this.$uGetRect(".u-notice__content__text")).width,(await this.$uGetRect(".u-notice__content")).width,this.animationDuration=e/n(this.speed)+"s",this.animationPlayState="paused",setTimeout((()=>{this.animationPlayState="running"}),10)},async nvue(){},loopAnimation(e,t){},getNvueRect(e){},clickHandler(e){this.$emit("click")},close(){this.$emit("close")}}},[["render",function(e,t,o,a,i,l){const n=s(c("u-icon"),U),r=x,g=S;return u(),d(r,{class:"u-notice",onClick:l.clickHandler},{default:m((()=>[y(e.$slots,"icon",{},(()=>[e.icon?(u(),d(r,{key:0,class:"u-notice__left-icon"},{default:m((()=>[p(n,{name:e.icon,color:e.color,size:"19"},null,8,["name","color"])])),_:1})):h("v-if",!0)]),!0),p(r,{class:"u-notice__content",ref:"u-notice__content"},{default:m((()=>[p(r,{ref:"u-notice__content__text",class:"u-notice__content__text",style:f([l.animationStyle])},{default:m((()=>[(u(!0),w(B,null,I(l.innerText,((e,t)=>(u(),d(g,{key:t,style:f([l.textStyle])},{default:m((()=>[_(b(e),1)])),_:2},1032,["style"])))),128))])),_:1},8,["style"])])),_:1},512),["link","closable"].includes(e.mode)?(u(),d(r,{key:0,class:"u-notice__right-icon"},{default:m((()=>["link"===e.mode?(u(),d(n,{key:0,name:"arrow-right",size:17,color:e.color},null,8,["color"])):h("v-if",!0),"closable"===e.mode?(u(),d(n,{key:1,onClick:l.close,name:"close",size:16,color:e.color},null,8,["onClick","color"])):h("v-if",!0)])),_:1})):h("v-if",!0)])),_:3},8,["onClick"])}],["__scopeId","data-v-e09af7bd"]]);const X=H({name:"u-notice-bar",mixins:[t,o,{props:{text:{type:[Array,String],default:()=>e.noticeBar.text},direction:{type:String,default:()=>e.noticeBar.direction},step:{type:Boolean,default:()=>e.noticeBar.step},icon:{type:String,default:()=>e.noticeBar.icon},mode:{type:String,default:()=>e.noticeBar.mode},color:{type:String,default:()=>e.noticeBar.color},bgColor:{type:String,default:()=>e.noticeBar.bgColor},speed:{type:[String,Number],default:()=>e.noticeBar.speed},fontSize:{type:[String,Number],default:()=>e.noticeBar.fontSize},duration:{type:[String,Number],default:()=>e.noticeBar.duration},disableTouch:{type:Boolean,default:()=>e.noticeBar.disableTouch},url:{type:String,default:()=>e.noticeBar.url},linkType:{type:String,default:()=>e.noticeBar.linkType}}}],data:()=>({show:!0}),emits:["click","close"],methods:{addStyle:a,click(e){this.$emit("click",e),this.url&&this.linkType&&this.openPage()},close(){this.show=!1,this.$emit("close")}}},[["render",function(e,t,o,a,i,l){const n=s(c("u-column-notice"),M),r=s(c("u-row-notice"),W),p=x;return i.show?(u(),d(p,{key:0,class:"u-notice-bar",style:f([{backgroundColor:e.bgColor},l.addStyle(e.customStyle)])},{default:m((()=>["column"===e.direction||"row"===e.direction&&e.step?(u(),d(n,{key:0,color:e.color,bgColor:e.bgColor,text:e.text,mode:e.mode,step:e.step,icon:e.icon,"disable-touch":e.disableTouch,fontSize:e.fontSize,duration:e.duration,onClose:l.close,onClick:l.click},null,8,["color","bgColor","text","mode","step","icon","disable-touch","fontSize","duration","onClose","onClick"])):(u(),d(r,{key:1,color:e.color,bgColor:e.bgColor,text:e.text,mode:e.mode,fontSize:e.fontSize,speed:e.speed,url:e.url,linkType:e.linkType,icon:e.icon,onClose:l.close,onClick:l.click},null,8,["color","bgColor","text","mode","fontSize","speed","url","linkType","icon","onClose","onClick"]))])),_:1},8,["style"])):h("v-if",!0)}],["__scopeId","data-v-53928391"]]);const J=H({name:"UpdatePublish",data:()=>({formData:{project_id:"",title:"",content:"",images:[],is_public:1},projectOptions:[],selectedProject:null,showProjectPicker:!1,readonlyProject:!1,loading:!1,submitting:!1}),computed:{canSubmit(){return this.formData.project_id&&this.formData.title.trim()&&this.formData.content.trim()&&!this.submitting}},onLoad(e){e.project_id&&(this.formData.project_id=parseInt(e.project_id),this.readonlyProject=!0),this.loadMyProjects()},methods:{async loadMyProjects(){this.loading=!0;try{const e=await q({status:1});this.projectOptions=e.data.data||[],this.formData.project_id&&(this.selectedProject=this.projectOptions.find((e=>e.project_id===this.formData.project_id)))}catch(e){console.error("加载项目列表失败:",e),this.$u.toast("加载项目列表失败")}finally{this.loading=!1}},onProjectSelect(e){this.selectedProject=this.projectOptions[e],this.formData.project_id=this.selectedProject.project_id,this.showProjectPicker=!1},chooseImage(){const e=9-this.formData.images.length;T({count:e,sizeType:["compressed"],sourceType:["album","camera"],success:e=>{this.uploadImages(e.tempFilePaths)}})},async uploadImages(e){for(let o of e)try{this.formData.images.push(o)}catch(t){console.error("图片上传失败:",t),this.$u.toast("图片上传失败")}},removeImage(e){this.formData.images.splice(e,1)},previewImage(e){z({urls:this.formData.images,current:e})},async submitUpdate(){if(this.canSubmit)if(this.formData.title.trim())if(this.formData.content.trim()){this.submitting=!0;try{const o=await(t=this.formData.project_id,D.get(`niucrowd/member/project/update/check-permission/${t}`));if(1!==o.code)return void this.$u.toast(o.msg||"没有发布权限");const a=await(e=this.formData,D.post("niucrowd/member/project/update",e));1===a.code?(this.$u.toast("动态发布成功，等待审核"),setTimeout((()=>{r()}),1500)):this.$u.toast(a.msg||"发布失败")}catch(o){console.error("发布动态失败:",o),this.$u.toast("发布失败")}finally{this.submitting=!1}var e,t}else this.$u.toast("请输入动态内容");else this.$u.toast("请输入动态标题")},validateForm(){return this.formData.project_id?this.formData.title.trim()?this.formData.title.length>200?(this.$u.toast("标题不能超过200个字符"),!1):this.formData.content.trim()?!(this.formData.content.length>2e3)||(this.$u.toast("内容不能超过2000个字符"),!1):(this.$u.toast("请输入动态内容"),!1):(this.$u.toast("请输入动态标题"),!1):(this.$u.toast("请选择项目"),!1)}}},[["render",function(e,t,o,a,i,l){const n=s(c("u-navbar"),R),r=S,f=x,y=s(c("u-icon"),U),k=s(c("u-picker"),K),v=s(c("u-input"),E),C=s(c("u-textarea"),G),j=V,P=s(c("u-radio"),A),N=s(c("u-radio-group"),O),$=s(c("u-notice-bar"),X),D=s(c("u-button"),L);return u(),d(f,{class:"update-publish-page"},{default:m((()=>[h(" 导航栏 "),p(n,{title:"发布项目动态","border-bottom":!1,background:{backgroundColor:"#007aff"}}),p(f,{class:"form-container"},{default:m((()=>[h(" 项目名称或选择 "),p(f,{class:"form-item"},{default:m((()=>[p(f,{class:"form-label"},{default:m((()=>[_("项目名称 "),p(r,{class:"required"},{default:m((()=>[_("*")])),_:1})])),_:1}),i.readonlyProject?(u(),d(f,{key:0,class:"project-name"},{default:m((()=>[_(b(i.selectedProject?i.selectedProject.project_name:""),1)])),_:1})):(u(),d(k,{key:1,modelValue:i.showProjectPicker,"onUpdate:modelValue":t[0]||(t[0]=e=>i.showProjectPicker=e),range:i.projectOptions,"range-key":"project_name",onConfirm:l.onProjectSelect},{default:m((()=>[p(f,{class:g(["picker-input",{placeholder:!i.formData.project_id}])},{default:m((()=>[_(b(i.selectedProject?i.selectedProject.project_name:"请选择要发布动态的项目")+" ",1),p(y,{name:"arrow-down",size:"14",color:"#999"})])),_:1},8,["class"])])),_:1},8,["modelValue","range","onConfirm"]))])),_:1}),h(" 动态标题 "),p(f,{class:"form-item"},{default:m((()=>[p(f,{class:"form-label"},{default:m((()=>[_("动态标题 "),p(r,{class:"required"},{default:m((()=>[_("*")])),_:1})])),_:1}),p(v,{modelValue:i.formData.title,"onUpdate:modelValue":t[1]||(t[1]=e=>i.formData.title=e),placeholder:"请输入动态标题（最多200字符）",maxlength:200,"show-word-limit":!0,border:!0},null,8,["modelValue"])])),_:1}),h(" 动态内容 "),p(f,{class:"form-item"},{default:m((()=>[p(f,{class:"form-label"},{default:m((()=>[_("动态内容 "),p(r,{class:"required"},{default:m((()=>[_("*")])),_:1})])),_:1}),p(C,{modelValue:i.formData.content,"onUpdate:modelValue":t[2]||(t[2]=e=>i.formData.content=e),placeholder:"请详细描述项目进展情况...",maxlength:2e3,"show-word-limit":!0,"auto-height":!0,"min-height":200},null,8,["modelValue"])])),_:1}),h(" 图片上传 "),p(f,{class:"form-item"},{default:m((()=>[p(f,{class:"form-label"},{default:m((()=>[_("动态图片 "),p(r,{class:"optional"},{default:m((()=>[_("（可选，最多9张）")])),_:1})])),_:1}),p(f,{class:"image-upload-container"},{default:m((()=>[p(f,{class:"image-list"},{default:m((()=>[(u(!0),w(B,null,I(i.formData.images,((e,t)=>(u(),d(f,{key:t,class:"image-item"},{default:m((()=>[p(j,{src:e,mode:"aspectFill",class:"uploaded-image",onClick:e=>l.previewImage(t)},null,8,["src","onClick"]),p(f,{class:"image-delete",onClick:e=>l.removeImage(t)},{default:m((()=>[p(y,{name:"close",size:"12",color:"#fff"})])),_:2},1032,["onClick"])])),_:2},1024)))),128)),i.formData.images.length<9?(u(),d(f,{key:0,class:"image-upload-btn",onClick:l.chooseImage},{default:m((()=>[p(y,{name:"plus",size:"24",color:"#999"}),p(r,{class:"upload-text"},{default:m((()=>[_("添加图片")])),_:1})])),_:1},8,["onClick"])):h("v-if",!0)])),_:1})])),_:1})])),_:1}),h(" 公开设置 "),p(f,{class:"form-item"},{default:m((()=>[p(f,{class:"form-label"},{default:m((()=>[_("公开设置")])),_:1}),p(N,{modelValue:i.formData.is_public,"onUpdate:modelValue":t[3]||(t[3]=e=>i.formData.is_public=e),placement:"row"},{default:m((()=>[p(P,{name:1,label:"公开（所有人可见）"}),p(P,{name:0,label:"仅支持者可见"})])),_:1},8,["modelValue"])])),_:1}),h(" 提示信息 "),p(f,{class:"tips-section"},{default:m((()=>[p($,{text:"动态发布后需要管理员审核，审核通过后才会在项目详情页显示",type:"warning","show-icon":!0})])),_:1}),h(" 提交按钮 "),p(f,{class:"submit-section"},{default:m((()=>[p(D,{type:"primary",loading:i.submitting,onClick:l.submitUpdate,disabled:!l.canSubmit},{default:m((()=>[_(b(i.submitting?"发布中...":"发布动态"),1)])),_:1},8,["loading","onClick","disabled"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-2eebb7d1"]]);export{J as default};
