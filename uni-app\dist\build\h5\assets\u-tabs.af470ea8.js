import{a4 as e,a5 as t,a6 as s,a8 as a,bc as i,a7 as r,bl as l,aK as n,bd as d,i as o,j as c,o as _,c as h,w as b,b as p,$ as u,R as g,S as m,a3 as f,T as w,n as y,A as v,B as S,I as x,k as C,ap as B}from"./index-12d8dd28.js";import{_ as k}from"./u-badge.19b04045.js";import{_ as R}from"./_plugin-vue_export-helper.1b428a4d.js";const T=R({name:"u-tabs",mixins:[t,s,{props:{duration:{type:Number,default:()=>e.tabs.duration},list:{type:Array,default:()=>e.tabs.list},lineColor:{type:String,default:()=>e.tabs.lineColor},activeStyle:{type:[String,Object],default:()=>e.tabs.activeStyle},inactiveStyle:{type:[String,Object],default:()=>e.tabs.inactiveStyle},lineWidth:{type:[String,Number],default:()=>e.tabs.lineWidth},lineHeight:{type:[String,Number],default:()=>e.tabs.lineHeight},lineBgSize:{type:String,default:()=>e.tabs.lineBgSize},itemStyle:{type:[String,Object],default:()=>e.tabs.itemStyle},scrollable:{type:Boolean,default:()=>e.tabs.scrollable},current:{type:[Number,String],default:()=>e.tabs.current},keyName:{type:String,default:()=>e.tabs.keyName}}}],data:()=>({firstTime:!0,scrollLeft:0,scrollViewWidth:0,lineOffsetLeft:0,tabsRect:{left:0},innerCurrent:0,moving:!1}),watch:{current:{immediate:!0,handler(e,t){e!==this.innerCurrent&&(this.innerCurrent=e,this.$nextTick((()=>{this.resize()})))}},list(){this.$nextTick((()=>{this.resize()}))}},computed:{textStyle(){return e=>{const t={},s=e===this.innerCurrent?a(this.activeStyle):a(this.inactiveStyle);return this.list[e].disabled&&(t.color="#c8c9cc"),i(s,t)}},propsBadge:()=>e.badge},async mounted(){this.init()},emits:["click","change"],methods:{addStyle:a,addUnit:r,setLineLeft(){const e=this.list[this.innerCurrent];if(!e)return;let t=this.list.slice(0,this.innerCurrent).reduce(((e,t)=>e+t.rect.width),0);const s=l(this.lineWidth);this.lineOffsetLeft=t+(e.rect.width-s)/2,this.firstTime&&setTimeout((()=>{this.firstTime=!1}),10)},animation(e,t=0){},clickHandler(e,t){this.$emit("click",{...e,index:t},t),e.disabled||(this.innerCurrent=t,this.resize(),this.$emit("change",{...e,index:t},t))},init(){n().then((()=>{this.resize()}))},setScrollLeft(){const e=this.list[this.innerCurrent],t=this.list.slice(0,this.innerCurrent).reduce(((e,t)=>e+t.rect.width),0),s=d().windowWidth;let a=t-(this.tabsRect.width-e.rect.width)/2-(s-this.tabsRect.right)/2+this.tabsRect.left/2;a=Math.min(a,this.scrollViewWidth-this.tabsRect.width),this.scrollLeft=Math.max(0,a)},resize(){0!==this.list.length&&Promise.all([this.getTabsRect(),this.getAllItemRect()]).then((([e,t=[]])=>{this.tabsRect=e,this.scrollViewWidth=0,t.map(((e,t)=>{this.scrollViewWidth+=e.width,this.list[t].rect=e})),this.setLineLeft(),this.setScrollLeft()}))},getTabsRect(){return new Promise((e=>{this.queryRect("u-tabs__wrapper__scroll-view").then((t=>e(t)))}))},getAllItemRect(){return new Promise((e=>{const t=this.list.map(((e,t)=>this.queryRect(`u-tabs__wrapper__nav__item-${t}`,!0)));Promise.all(t).then((t=>e(t)))}))},queryRect(e,t){return new Promise((t=>{this.$uGetRect(`.${e}`).then((e=>{t(e)}))}))}}},[["render",function(e,t,s,a,i,r){const l=x,n=o(c("u-badge"),k),d=C,R=B;return _(),h(d,{class:"u-tabs"},{default:b((()=>[p(d,{class:"u-tabs__wrapper"},{default:b((()=>[u(e.$slots,"left",{},void 0,!0),p(d,{class:"u-tabs__wrapper__scroll-view-wrapper"},{default:b((()=>[p(R,{"scroll-x":e.scrollable,"scroll-left":i.scrollLeft,"scroll-with-animation":"",class:"u-tabs__wrapper__scroll-view","show-scrollbar":!1,ref:"u-tabs__wrapper__scroll-view"},{default:b((()=>[p(d,{class:"u-tabs__wrapper__nav",ref:"u-tabs__wrapper__nav"},{default:b((()=>[(_(!0),g(m,null,f(e.list,((t,s)=>(_(),h(d,{class:w(["u-tabs__wrapper__nav__item",[`u-tabs__wrapper__nav__item-${s}`,t.disabled&&"u-tabs__wrapper__nav__item--disabled"]]),key:s,onClick:e=>r.clickHandler(t,s),ref_for:!0,ref:`u-tabs__wrapper__nav__item-${s}`,style:y([r.addStyle(e.itemStyle),{flex:e.scrollable?"":1}])},{default:b((()=>[p(l,{class:w([[t.disabled&&"u-tabs__wrapper__nav__item__text--disabled"],"u-tabs__wrapper__nav__item__text"]),style:y([r.textStyle(s)])},{default:b((()=>[v(S(t[e.keyName]),1)])),_:2},1032,["class","style"]),p(n,{show:!(!t.badge||!(t.badge.show||t.badge.isDot||t.badge.value)),isDot:t.badge&&t.badge.isDot||r.propsBadge.isDot,value:t.badge&&t.badge.value||r.propsBadge.value,max:t.badge&&t.badge.max||r.propsBadge.max,type:t.badge&&t.badge.type||r.propsBadge.type,showZero:t.badge&&t.badge.showZero||r.propsBadge.showZero,bgColor:t.badge&&t.badge.bgColor||r.propsBadge.bgColor,color:t.badge&&t.badge.color||r.propsBadge.color,shape:t.badge&&t.badge.shape||r.propsBadge.shape,numberType:t.badge&&t.badge.numberType||r.propsBadge.numberType,inverted:t.badge&&t.badge.inverted||r.propsBadge.inverted,customStyle:"margin-left: 4px;"},null,8,["show","isDot","value","max","type","showZero","bgColor","color","shape","numberType","inverted"])])),_:2},1032,["onClick","style","class"])))),128)),p(d,{class:"u-tabs__wrapper__nav__line",ref:"u-tabs__wrapper__nav__line",style:y([{width:r.addUnit(e.lineWidth),transform:`translate(${i.lineOffsetLeft}px)`,transitionDuration:`${i.firstTime?0:e.duration}ms`,height:r.addUnit(e.lineHeight),background:e.lineColor,backgroundSize:e.lineBgSize}])},null,8,["style"])])),_:1},512)])),_:1},8,["scroll-x","scroll-left"])])),_:1}),u(e.$slots,"right",{},void 0,!0)])),_:3})])),_:3})}],["__scopeId","data-v-caa7e19d"]]);export{T as _};
