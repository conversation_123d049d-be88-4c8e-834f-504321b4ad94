import{r as t,ai as l,aW as a}from"./index-12d8dd28.js";function e(){const e=t({title:"",topStatusBar:{style:"style-1",bgColor:"transparent",rollBgColor:"#fff",textColor:"#fff",rollTextColor:"#333"}}),o=t(1),r=t(-1);l((t=>{t.scrollTop<=0?r.value=-1:t.scrollTop>o.value?r.value=1:r.value=2}));return{getScrollBool:()=>r.value,setTopTabbarParam:(t={})=>{if(t&&"object"!=typeof t)return e;for(let l in t)if("title"==l)e.value.title=t.title||"";else if("topStatusBar"==l&&t.topStatusBar)for(let a in t.topStatusBar)e.value.topStatusBar[a]=t.topStatusBar[a];else e.value[l]=t[l];return a(e.value)}}}export{e as t};
