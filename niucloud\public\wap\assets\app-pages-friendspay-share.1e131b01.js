import{d as e,r as a,u as t,p as l,l as s,s as r,z as i,al as u,am as o,L as n,an as p,M as d,o as _,c as f,w as m,b as c,e as x,A as v,B as g,n as y,g as b,R as h,S as j,a3 as F,i as k,j as w,I as T,k as P,H as O,E as I,F as S,a as $,G as B}from"./index-12d8dd28.js";import{_ as C}from"./u-avatar.47324b30.js";import{_ as U}from"./u--image.376d9df2.js";import{_ as J}from"./loading-page.vue_vue_type_script_setup_true_lang.1c76425e.js";import{g as N,_ as R}from"./message.vue_vue_type_script_setup_true_lang.25667d1f.js";import{t as z}from"./topTabbar.16728c6f.js";import{s as E}from"./share-poster.8b3efc88.js";import{_ as L}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-icon.52b2e8b4.js";/* empty css                                                               */import"./u-text.40aa3da3.js";import"./u-image.f86d3186.js";import"./u-transition.5e23e77b.js";/* empty css                                                                     *//* empty css                                                                */import"./u-loading-icon.85553345.js";import"./u-popup.155f7cb9.js";import"./u-safe-bottom.a6f7ba25.js";const M=L(e({__name:"share",setup(e){const L=z();L.setTopTabbarParam({title:""});const M=a(!0),V=a(!1),q=a(0),A=a(""),G=a({}),H=a(!0),Q=a(null),{setShare:D}=t(),K=l(),W=s((()=>K.info));r((e=>{q.value=e.id||0,A.value=e.type||""})),i((()=>{q.value&&A.value&&X(A.value,q.value)})),u((()=>{Q.value&&(clearTimeout(Q.value),Q.value=null)})),o((()=>{Q.value&&(clearTimeout(Q.value),Q.value=null)}));const X=(e,a)=>{H.value&&(M.value=!0,H.value=!1),N(e,a).then((t=>{G.value=t.data,M.value=!1,n({title:G.value.config.pay_page_name}),L.setTopTabbarParam({title:G.value.config.pay_page_name});let l="",s=location.pathname,r=["/app/","/addon/"];for(let e=0;e<r.length;e++)-1!=s.indexOf(r[e])&&(s=s.substr(0,s.indexOf(r[e])));l=location.origin+s+`/app/pages/friendspay/money?id=${G.value.trade_id}&type=${G.value.trade_type}`;let i={desc:G.value.config.pay_leave_message,path:`/app/pages/friendspay/money?id=${G.value.trade_id}&type=${G.value.trade_type}`,link:l};if(G.value.member){let e=G.value.member.nickname;e=e.length>15?e=e.substring(0,15)+"...":e,i.title=`${e}希望你帮他付${G.value.money}元`}"[]"!==JSON.stringify(G.value.trade_info)&&G.value.trade_info.item_list.length?i.url=G.value.trade_info.item_list[0].item_image?G.value.trade_info.item_list[0].item_image:G.value.config.pay_wechat_share_image:i.url=G.value.config.pay_wechat_share_image,D({wechat:{...i},weapp:{...i}}),se(),p((()=>{setTimeout((()=>{ee.value&&(le.id=G.value.trade_id,le.type=G.value.trade_type,W.value&&W.value.member_id&&(le.member_id=W.value.member_id),ee.value.loadPoster())}),400)})),2!=G.value.status&&1!=G.value.status&&-1!=G.value.status?Q.value=setTimeout((()=>{X(e,a)}),3e3):(clearTimeout(Q.value),Q.value=null)})).catch((e=>{Q.value&&(clearTimeout(Q.value),Q.value=null),M.value=!1;d({title:"未找到帮付订单信息",url:"/app/pages/index/index",mode:"reLaunch"})}))},Y=a(null),Z=()=>{Y.value.open(G.value.config)},ee=a(null),ae=a("/app/pages/friendspay/money"),te=a("");let le={};const se=()=>{te.value="?id="+G.value.trade_id,te.value+="&type="+G.value.trade_type},re=()=>{le.id=G.value.trade_id,le.type=G.value.trade_type,W.value&&W.value.member_id&&(le.member_id=W.value.member_id),ee.value.openShare()};return(e,a)=>{const t=k(w("u-avatar"),C),l=T,s=P,r=O,i=B,u=k(w("u--image"),U),o=k(w("loading-page"),J);return _(),f(s,{style:y(e.themeColor())},{default:m((()=>[Object.keys(G.value).length&&!M.value?(_(),f(s,{key:0,class:"bg-[var(--page-bg-color)] min-h-screen overflow-hidden"},{default:m((()=>[c(s,{style:y({background:"url("+x(I)("static/resource/images/app/friendpay_bg.png")+") left bottom /100% no-repeat"}),class:"pb-[168rpx] overflow-hidden"},{default:m((()=>[c(s,{class:"mt-[20rpx] flex flex-col items-center"},{default:m((()=>[c(t,{src:x(I)(G.value.member.headimg),size:"50",leftIcon:"none","default-url":x(I)("static/resource/images/default_headimg.png")},null,8,["src","default-url"]),c(s,{class:"flex items-center mt-[20rpx] text-[#fff] text-[26rpx] leading-[36rpx]"},{default:m((()=>[c(l,{class:"font-bold mr-[10rpx] max-w-[250rpx] truncate"},{default:m((()=>[v(g(G.value.member.nickname),1)])),_:1}),c(l,null,{default:m((()=>[v("发起了订单帮付请求~")])),_:1})])),_:1})])),_:1})])),_:1},8,["style"]),c(s,{class:"mt-[-128rpx] card-template sidebar-margin mb-[var(--top-m)]"},{default:m((()=>[c(s,{class:"text-[24rpx] text-center text-[#333] mb-[10rpx]"},{default:m((()=>[v(g(x(S)("payMoney")),1)])),_:1}),c(s,{class:"text-center mb-[50rpx]"},{default:m((()=>[c(l,{class:"text-[32rpx] font-500 price-font text-[#FF4142]"},{default:m((()=>[v("￥")])),_:1}),c(l,{class:"text-[56rpx] font-bold price-font text-[#FF4142]"},{default:m((()=>[v(g(parseFloat(G.value.money).toFixed(2).split(".")[0]),1)])),_:1}),c(l,{class:"text-[32rpx] font-500 price-font text-[#FF4142]"},{default:m((()=>[v("."+g(parseFloat(G.value.money).toFixed(2).split(".")[1]),1)])),_:1})])),_:1}),c(s,{class:"px-[20rpx] box-border"},{default:m((()=>[2==G.value.status?(_(),f(r,{key:0,class:"bg-[#FFB4B1] !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500","hover-class":"none"},{default:m((()=>[v(g(x(S)("finish")),1)])),_:1})):-1==G.value.status?(_(),f(r,{key:1,class:"bg-[#FFB4B1] !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500","hover-class":"none"},{default:m((()=>[v(g(x(S)("close")),1)])),_:1})):(_(),f(r,{key:2,class:"button-color !text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500","hover-class":"none",loading:V.value,onClick:re},{default:m((()=>[v(g(G.value.config.pay_type_name?G.value.config.pay_type_name:x(S)("friendPay")),1)])),_:1},8,["loading"]))])),_:1}),2==G.value.status&&"[]"!==JSON.stringify(G.value.trade_info)&&G.value.trade_info.detail_url?(_(),f(s,{key:0,class:"mt-[20rpx] flex items-baseline justify-center text-[var(--text-color-light9)]",onClick:a[0]||(a[0]=e=>x($)({url:G.value.trade_info.detail_url}))},{default:m((()=>[c(l,{class:"text-[24rpx] mr-[6rpx]"},{default:m((()=>[v("查看订单")])),_:1}),c(l,{class:"nc-iconfont nc-icon-youV6xx text-[26rpx]"})])),_:1})):b("v-if",!0)])),_:1}),c(s,{class:"card-template sidebar-margin mb-[var(--top-m)]"},{default:m((()=>[c(s,{class:"flex justify-between items-center mb-[30rpx]"},{default:m((()=>[c(s,{class:"text-[30rpx] text-[#333] font-500"},{default:m((()=>[v(g(x(S)("friendPayOrderInfo")),1)])),_:1}),G.value.config.pay_explain_switch?(_(),f(s,{key:0,class:"text-[#666] leading-[1]",onClick:Z},{default:m((()=>[c(l,{class:"mr-[8rpx] text-[24rpx]"},{default:m((()=>[v(g(G.value.config.pay_explain_title),1)])),_:1}),c(l,{class:"nc-iconfont nc-icon-jichuxinxiV6xx text-[26rpx]"})])),_:1})):b("v-if",!0)])),_:1}),"[]"!==JSON.stringify(G.value.trade_info)?(_(),h(j,{key:0},[G.value.trade_info.item_list.length?(_(),h(j,{key:0},[c(s,{class:"border-0 border-solid border-b-[1rpx] border-[#f6f6f6] mb-[20rpx]"},{default:m((()=>[(_(!0),h(j,null,F(G.value.trade_info.item_list,((e,a)=>(_(),f(s,{class:"flex justify-between mb-[30rpx]"},{default:m((()=>[c(s,{class:"w-[170rpx] h-[170rpx] rounded-[var(--goods-rounded-big)] overflow-hidden flex-shrink-0"},{default:m((()=>[c(u,{class:"overflow-hidden",radius:"var(--goods-rounded-big)",width:"170rpx",height:"170rpx",src:x(I)(e.item_image?e.item_image:""),model:"aspectFill"},{error:m((()=>[c(i,{class:"w-[170rpx] h-[170rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:x(I)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:2},1032,["src"])])),_:2},1024),c(s,{class:"ml-[20rpx] flex flex-1 flex-col justify-between"},{default:m((()=>[c(s,null,{default:m((()=>[c(s,{class:"text-[28rpx] using-hidden leading-[40rpx] text-[#333]"},{default:m((()=>[v(g(e.item_name),1)])),_:2},1024),e.item_sub_name?(_(),f(s,{key:0,class:"text-[24rpx] mt-[14rpx] text-[var(--text-color-light9)] using-hidden leading-[28rpx]"},{default:m((()=>[v(g(e.item_sub_name),1)])),_:2},1024)):b("v-if",!0)])),_:2},1024),c(s,{class:"flex justify-between items-baseline"},{default:m((()=>[c(s,{class:"price-font text-[#FF4142]"},{default:m((()=>[c(l,{class:"text-[24rpx]"},{default:m((()=>[v("￥")])),_:1}),c(l,{class:"text-[40rpx] font-500"},{default:m((()=>[v(g(parseFloat(e.item_price).toFixed(2).split(".")[0]),1)])),_:2},1024),c(l,{class:"text-[24rpx] font-500"},{default:m((()=>[v("."+g(parseFloat(e.item_price).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),c(l,{class:"text-right text-[26rpx]"},{default:m((()=>[v("x"+g(e.item_num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),256))])),_:1}),c(s,{class:"text-[26rpx] text-right"},{default:m((()=>[v(g(G.value.trade_info.item_total),1)])),_:1})],64)):b("v-if",!0)],64)):(_(),f(s,{key:1,class:"text-[28rpx] leading-[40rpx] text-[#333]"},{default:m((()=>[v(g(G.value.body),1)])),_:1}))])),_:1}),c(E,{ref_key:"sharePosterRef",ref:ee,posterType:"friendspay",posterId:G.value.poster_id,posterParam:x(le),copyUrl:ae.value,copyUrlParam:te.value},null,8,["posterId","posterParam","copyUrl","copyUrlParam"]),b(" 帮付说明 "),c(R,{ref_key:"messageRef",ref:Y},null,512)])),_:1})):b("v-if",!0),c(o,{loading:M.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-56c4fb99"]]);export{M as default};
