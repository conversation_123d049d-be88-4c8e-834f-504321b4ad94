import{bz as s,y as a,bM as t,bN as e,b8 as l,o as i,c as u,w as c,k as d,g as o,b as n,A as r,R as f,T as _,B as m,S as h,a0 as p,I as S,H as b}from"./index-12d8dd28.js";import{a as g,c as k}from"./member-auth.edd63e6a.js";import{_ as y}from"./_plugin-vue_export-helper.1b428a4d.js";const P=y({name:"AuthStatus",data:()=>({loading:!0,authStatus:{status:-1,status_text:"未认证",message:"",can_submit:!0,data:null},publishPermission:null}),onLoad(){this.loadStatusAndPermission()},onShow(){this.loadStatusAndPermission()},onPullDownRefresh(){this.loadStatusAndPermission().finally((()=>{s()}))},methods:{async loadStatusAndPermission(){try{this.loading=!0;const{data:s}=await g();this.authStatus=s.auth_status,this.publishPermission=s.publish_permission}catch(s){console.error("获取状态失败:",s),a({title:"获取状态失败",icon:"none"})}finally{this.loading=!1}},async checkPublishPermission(){try{t({title:"检查中..."});const{data:s}=await k();this.publishPermission=s,a({title:s.allow?"可以发布项目":"暂不能发布",icon:s.allow?"success":"none"})}catch(s){console.error("检查权限失败:",s),a({title:"检查权限失败",icon:"none"})}finally{e()}},goToAuth(){l({url:"/addon/niucrowd/pages/member/auth"})},goToCreateProject(){l({url:"/addon/niucrowd/pages/project/create"})},getStatusClass:s=>({0:"status-pending",1:"status-approved",2:"status-rejected"}[s]||"status-default"),getStatusIcon:s=>({0:"icon-clock",1:"icon-check",2:"icon-close"}[s]||"icon-info"),getStepStatus(s){const a=this.authStatus.status;return-1===a?1===s:0===a?s<=2:1===a?s<=3:2===a&&1===s},formatTime(s){if(!s)return"-";return new Date(1e3*s).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}}},[["render",function(s,a,t,e,l,g){const k=d,y=p("uni-load-more"),P=S,v=b;return i(),u(k,{class:"auth-status-container"},{default:c((()=>[o(" 页面标题 "),n(k,{class:"page-header"},{default:c((()=>[n(k,{class:"header-title"},{default:c((()=>[r("认证状态")])),_:1}),n(k,{class:"header-desc"},{default:c((()=>[r("查看您的身份认证状态和详情")])),_:1})])),_:1}),o(" 加载状态 "),l.loading?(i(),u(k,{key:0,class:"loading-container"},{default:c((()=>[n(y,{status:"loading"})])),_:1})):(i(),f(h,{key:1},[o(" 认证状态卡片 "),n(k,{class:"status-card"},{default:c((()=>[n(k,{class:"status-header"},{default:c((()=>[n(k,{class:_(["status-icon",g.getStatusClass(l.authStatus.status)])},{default:c((()=>[n(P,{class:_(["iconfont",g.getStatusIcon(l.authStatus.status)])},null,8,["class"])])),_:1},8,["class"]),n(k,{class:"status-info"},{default:c((()=>[n(k,{class:"status-text"},{default:c((()=>[r(m(l.authStatus.status_text),1)])),_:1}),n(k,{class:"status-message"},{default:c((()=>[r(m(l.authStatus.message),1)])),_:1})])),_:1})])),_:1}),o(" 认证详情 "),l.authStatus.data?(i(),u(k,{key:0,class:"auth-details"},{default:c((()=>[n(k,{class:"detail-title"},{default:c((()=>[r("认证信息")])),_:1}),n(k,{class:"detail-item"},{default:c((()=>[n(k,{class:"detail-label"},{default:c((()=>[r("真实姓名")])),_:1}),n(k,{class:"detail-value"},{default:c((()=>[r(m(l.authStatus.data.real_name),1)])),_:1})])),_:1}),n(k,{class:"detail-item"},{default:c((()=>[n(k,{class:"detail-label"},{default:c((()=>[r("身份证号")])),_:1}),n(k,{class:"detail-value"},{default:c((()=>[r(m(l.authStatus.data.id_card_no_mask),1)])),_:1})])),_:1}),n(k,{class:"detail-item"},{default:c((()=>[n(k,{class:"detail-label"},{default:c((()=>[r("银行名称")])),_:1}),n(k,{class:"detail-value"},{default:c((()=>[r(m(l.authStatus.data.bank_name),1)])),_:1})])),_:1}),n(k,{class:"detail-item"},{default:c((()=>[n(k,{class:"detail-label"},{default:c((()=>[r("提交时间")])),_:1}),n(k,{class:"detail-value"},{default:c((()=>[r(m(g.formatTime(l.authStatus.data.create_time)),1)])),_:1})])),_:1}),l.authStatus.data.audit_time?(i(),u(k,{key:0,class:"detail-item"},{default:c((()=>[n(k,{class:"detail-label"},{default:c((()=>[r("审核时间")])),_:1}),n(k,{class:"detail-value"},{default:c((()=>[r(m(g.formatTime(l.authStatus.data.audit_time)),1)])),_:1})])),_:1})):o("v-if",!0),l.authStatus.data.audit_remark?(i(),u(k,{key:1,class:"detail-item"},{default:c((()=>[n(k,{class:"detail-label"},{default:c((()=>[r("审核备注")])),_:1}),n(k,{class:"detail-value remark"},{default:c((()=>[r(m(l.authStatus.data.audit_remark),1)])),_:1})])),_:1})):o("v-if",!0)])),_:1})):o("v-if",!0),o(" 操作按钮 "),n(k,{class:"status-actions"},{default:c((()=>[l.authStatus.can_submit?(i(),u(v,{key:0,class:"btn-primary",onClick:g.goToAuth},{default:c((()=>[r(m(-1===l.authStatus.status?"立即认证":"重新认证"),1)])),_:1},8,["onClick"])):o("v-if",!0),n(v,{class:"btn-default",onClick:g.checkPublishPermission},{default:c((()=>[r(" 检查发布权限 ")])),_:1},8,["onClick"])])),_:1})])),_:1})],2112)),o(" 发布权限卡片 "),l.publishPermission?(i(),u(k,{key:2,class:"permission-card"},{default:c((()=>[n(k,{class:"permission-header"},{default:c((()=>[n(k,{class:_(["permission-icon",l.publishPermission.allow?"permission-allow":"permission-deny"])},{default:c((()=>[n(P,{class:_(["iconfont",l.publishPermission.allow?"icon-check":"icon-close"])},null,8,["class"])])),_:1},8,["class"]),n(k,{class:"permission-info"},{default:c((()=>[n(k,{class:"permission-text"},{default:c((()=>[r(m(l.publishPermission.allow?"可以发布项目":"暂不能发布项目"),1)])),_:1}),l.publishPermission.message?(i(),u(k,{key:0,class:"permission-message"},{default:c((()=>[r(m(l.publishPermission.message),1)])),_:1})):o("v-if",!0)])),_:1})])),_:1}),l.publishPermission.allow?(i(),u(k,{key:0,class:"permission-actions"},{default:c((()=>[n(v,{class:"btn-success",onClick:g.goToCreateProject},{default:c((()=>[r(" 立即发布项目 ")])),_:1},8,["onClick"])])),_:1})):o("v-if",!0)])),_:1})):o("v-if",!0),o(" 认证流程说明 "),n(k,{class:"process-card"},{default:c((()=>[n(k,{class:"process-title"},{default:c((()=>[n(P,{class:"iconfont icon-info"}),n(P,null,{default:c((()=>[r("认证流程")])),_:1})])),_:1}),n(k,{class:"process-steps"},{default:c((()=>[n(k,{class:_(["step-item",{active:g.getStepStatus(1)}])},{default:c((()=>[n(k,{class:"step-number"},{default:c((()=>[r("1")])),_:1}),n(k,{class:"step-content"},{default:c((()=>[n(k,{class:"step-title"},{default:c((()=>[r("提交资料")])),_:1}),n(k,{class:"step-desc"},{default:c((()=>[r("上传身份证和银行卡信息")])),_:1})])),_:1})])),_:1},8,["class"]),n(k,{class:_(["step-item",{active:g.getStepStatus(2)}])},{default:c((()=>[n(k,{class:"step-number"},{default:c((()=>[r("2")])),_:1}),n(k,{class:"step-content"},{default:c((()=>[n(k,{class:"step-title"},{default:c((()=>[r("等待审核")])),_:1}),n(k,{class:"step-desc"},{default:c((()=>[r("1-3个工作日内完成审核")])),_:1})])),_:1})])),_:1},8,["class"]),n(k,{class:_(["step-item",{active:g.getStepStatus(3)}])},{default:c((()=>[n(k,{class:"step-number"},{default:c((()=>[r("3")])),_:1}),n(k,{class:"step-content"},{default:c((()=>[n(k,{class:"step-title"},{default:c((()=>[r("认证完成")])),_:1}),n(k,{class:"step-desc"},{default:c((()=>[r("可以发布众筹项目")])),_:1})])),_:1})])),_:1},8,["class"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-a0a2e745"]]);export{P as default};
