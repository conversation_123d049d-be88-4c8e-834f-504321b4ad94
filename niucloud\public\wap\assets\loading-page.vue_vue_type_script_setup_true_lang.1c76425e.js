import{a4 as o,a5 as a,a6 as e,a7 as i,i as l,j as t,o as n,c as d,w as g,b as s,n as r,$ as p,A as u,B as c,G as f,k as _,I as m,d as y}from"./index-12d8dd28.js";import{_ as S}from"./u-loading-icon.85553345.js";import{_ as b}from"./u-transition.5e23e77b.js";import{_ as z}from"./_plugin-vue_export-helper.1b428a4d.js";const x=z({name:"u-loading-page",mixins:[a,e,{props:{loadingText:{type:[String,Number],default:()=>o.loadingPage.loadingText},image:{type:String,default:()=>o.loadingPage.image},loadingMode:{type:String,default:()=>o.loadingPage.loadingMode},loading:{type:Boolean,default:()=>o.loadingPage.loading},bgColor:{type:String,default:()=>o.loadingPage.bgColor},color:{type:String,default:()=>o.loadingPage.color},fontSize:{type:[String,Number],default:()=>o.loadingPage.fontSize},iconSize:{type:[String,Number],default:()=>o.loadingPage.fontSize},loadingColor:{type:String,default:()=>o.loadingPage.loadingColor}}}],data:()=>({}),methods:{addUnit:i}},[["render",function(o,a,e,i,y,z){const x=f,h=l(t("u-loading-icon"),S),w=_,C=m,P=l(t("u-transition"),b);return n(),d(P,{show:o.loading,"custom-style":{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:o.bgColor,display:"flex"}},{default:g((()=>[s(w,{class:"u-loading-page"},{default:g((()=>[s(w,{class:"u-loading-page__warpper"},{default:g((()=>[s(w,{class:"u-loading-page__warpper__loading-icon"},{default:g((()=>[o.image?(n(),d(x,{key:0,src:o.image,class:"u-loading-page__warpper__loading-icon__img",mode:"widthFit",style:r({width:z.addUnit(o.iconSize),height:z.addUnit(o.iconSize)})},null,8,["src","style"])):(n(),d(h,{key:1,mode:o.loadingMode,size:z.addUnit(o.iconSize),color:o.loadingColor},null,8,["mode","size","color"]))])),_:1}),p(o.$slots,"default",{},(()=>[s(C,{class:"u-loading-page__warpper__text",style:r({fontSize:z.addUnit(o.fontSize),color:o.color})},{default:g((()=>[u(c(o.loadingText),1)])),_:1},8,["style"])]),!0)])),_:3})])),_:3})])),_:3},8,["show","custom-style"])}],["__scopeId","data-v-306503b4"]]),h=y({__name:"loading-page",props:{loading:{type:Boolean,default:!1},iconSize:{type:String||Number,default:30},bgColor:{type:String,default:"rgb(248,248,248)"}},setup(o){const a=o;return(o,e)=>{const i=l(t("u-loading-page"),x),r=_;return n(),d(r,null,{default:g((()=>[s(i,{"bg-color":"props.bgColor","icon-size":"props.iconSize",loading:a.loading,loadingText:""},null,8,["loading"])])),_:1})}}});export{h as _};
