import{d as e,r as a,p as l,l as o,N as t,Q as r,F as s,o as i,c as n,w as u,b as d,A as p,B as m,e as c,C as g,g as b,T as x,m as f,y as _,O as v,V as h,J as y,k,ag as S,i as j,j as V,I as w,H as C,a as A}from"./index-12d8dd28.js";import{_ as T,a as B}from"./u-form.b43211a9.js";import{_ as O}from"./sms-code.vue_vue_type_script_setup_true_lang.bd6d03c4.js";import{_ as I}from"./u-checkbox.cf8c867e.js";import{_ as P}from"./u-checkbox-group.aeb040c3.js";import{_ as U}from"./u-popup.155f7cb9.js";import{_ as M}from"./_plugin-vue_export-helper.1b428a4d.js";const q=M(e({__name:"bind-mobile",setup(e,{expose:M}){const q=a(!1),z=l(),E=o((()=>z.info)),F=o((()=>f().login)),H=a(!1),J=a(!1),L=t({mobile:"",mobile_code:"",mobile_key:""}),N=a(!0);r((()=>{setTimeout((()=>{N.value=!1}),800),uni.getStorageSync("pid")&&Object.assign(L,{pid:uni.getStorageSync("pid")}),uni.getStorageSync("openid")&&Object.assign(L,{openid:uni.getStorageSync("openid")}),uni.getStorageSync("unionid")&&Object.assign(L,{unionid:uni.getStorageSync("unionid")}),uni.getStorageSync("nickname")&&Object.assign(L,{nickname:uni.getStorageSync("nickname")}),uni.getStorageSync("avatar")&&Object.assign(L,{headimg:uni.getStorageSync("avatar")})}));const Q={mobile:[{type:"string",required:!0,message:s("mobilePlaceholder"),trigger:["blur","change"]},{validator(e,a,l){uni.$u.test.mobile(a)?l():l(new Error("请输入正确的手机号"))},message:s("mobileError"),trigger:["change","blur"]}],mobile_code:{type:"string",required:!0,message:s("codePlaceholder"),trigger:["blur","change"]}},R=()=>{J.value=!J.value},Z=a(null),$=()=>{Z.value.validate().then((()=>{if(!J.value&&F.value.agreement_show)return void _({title:s("isAgreeTips"),icon:"none"});if(H.value)return;H.value=!0;(E.value?v:h)(L).then((e=>{E.value?(z.getMemberInfo(),E.value.mobile&&uni.removeStorageSync("isBindMobile")):(z.setToken(e.data.token),y().handleLoginBack()),q.value=!1})).catch((()=>{H.value=!1}))}))};return M({open:()=>{q.value=!0}}),(e,a)=>{const l=k,o=S,t=j(V("u-form-item"),T),r=j(V("sms-code"),O),f=j(V("u-checkbox"),I),_=j(V("u-checkbox-group"),P),v=w,h=C,y=j(V("u-form"),B),M=j(V("u-popup"),U);return i(),n(M,{show:q.value,onClose:a[7]||(a[7]=e=>q.value=!1),mode:"center",round:10,closeable:!0,safeAreaInsetBottom:!1,zIndex:"10086"},{default:u((()=>[d(l,{onTouchmove:a[6]||(a[6]=g((()=>{}),["prevent","stop"])),class:"max-w-[600rpx] w-[600rpx] box-border"},{default:u((()=>[d(l,{class:"text-center py-[var(--pad-top-m)] text-[32rpx] font-500 leading-[46rpx]"},{default:u((()=>[p(m(c(s)("bindMobile")),1)])),_:1}),d(l,{class:"px-[var(--pad-sidebar-m)] pb-[var(--pad-top-m)]"},{default:u((()=>[d(y,{labelPosition:"left",model:L,errorType:"toast",rules:Q,ref_key:"formRef",ref:Z},{default:u((()=>[d(t,{label:"",prop:"mobile",borderBottom:!0},{default:u((()=>[d(o,{modelValue:L.mobile,"onUpdate:modelValue":a[0]||(a[0]=e=>L.mobile=e),type:"number",maxlength:"11",placeholder:c(s)("mobilePlaceholder"),class:"w-full h-[50rpx] leading-[50rpx] !bg-transparent !px-[20rpx] text-[26rpx] text-[#333]",disabled:N.value,"placeholder-class":"bind-mobile"},null,8,["modelValue","placeholder","disabled"])])),_:1}),d(l,{class:"mt-[20rpx]"},{default:u((()=>[d(t,{label:"",prop:"mobile_code",borderBottom:!0},{right:u((()=>[c(F).agreement_show?(i(),n(r,{key:0,mobile:L.mobile,type:"login",modelValue:L.mobile_key,"onUpdate:modelValue":a[2]||(a[2]=e=>L.mobile_key=e),isAgree:J.value},null,8,["mobile","modelValue","isAgree"])):(i(),n(r,{key:1,mobile:L.mobile,type:"login",modelValue:L.mobile_key,"onUpdate:modelValue":a[3]||(a[3]=e=>L.mobile_key=e)},null,8,["mobile","modelValue"]))])),default:u((()=>[d(o,{modelValue:L.mobile_code,"onUpdate:modelValue":a[1]||(a[1]=e=>L.mobile_code=e),type:"number",maxlength:"6",placeholder:c(s)("codePlaceholder"),class:"box-border w-full h-[50rpx] leading-[50rpx] !bg-transparent !px-[20rpx] text-[26rpx] text-[#333]",disabled:N.value,"placeholder-class":"bind-mobile"},null,8,["modelValue","placeholder","disabled"])])),_:1})])),_:1}),c(F).agreement_show?(i(),n(l,{key:0,class:"flex items-center mt-[30rpx] pl-[10rpx] py-[10rpx]",onClick:g(R,["stop"])},{default:u((()=>[d(_,{onChange:R},{default:u((()=>[d(f,{activeColor:"var(--primary-color)",checked:J.value,shape:"circle",size:"28rpx"},null,8,["checked"])])),_:1}),d(l,{class:"text-[24rpx] text-[var(--text-color-light6)] flex items-center flex-wrap"},{default:u((()=>[d(v,null,{default:u((()=>[p(m(c(s)("agreeTips")),1)])),_:1}),d(v,{onClick:a[4]||(a[4]=g((e=>c(A)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:u((()=>[p("《"+m(c(s)("privacyAgreement"))+"》",1)])),_:1}),d(v,null,{default:u((()=>[p(m(c(s)("and")),1)])),_:1}),d(v,{onClick:a[5]||(a[5]=g((e=>c(A)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:u((()=>[p("《"+m(c(s)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"])):b("v-if",!0),d(l,{class:"mt-[100rpx]"},{default:u((()=>[d(h,{class:x(["primary-btn-bg text-[26rpx] !text-[#fff] !h-[80rpx] leading-[80rpx] rounded-full font-500",{"opacity-50":H.value}]),disabled:H.value,onClick:$},{default:u((()=>[p(m(c(s)("bind")),1)])),_:1},8,["class","disabled"])])),_:1})])),_:1},8,["model"])])),_:1})])),_:1})])),_:1},8,["show"])}}}),[["__scopeId","data-v-6fde1d0f"]]);export{q as b};
