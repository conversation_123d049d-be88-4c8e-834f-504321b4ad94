import{r as e,Q as a,o as s,c as t,w as l,k as c,g as r,b as u,A as o,R as n,a3 as i,S as d,B as _,y as f,bE as p,I as v,ag as m,n as h,b8 as y,G as g}from"./index-12d8dd28.js";import{_ as k}from"./_plugin-vue_export-helper.1b428a4d.js";const j=k({__name:"search",setup(k){const j=e(""),w=e(!1),b=e([]),C=e(0),S=e([]),x=e(["科技创新","公益慈善","创意设计","智能硬件","文化艺术"]),$=e("/static/resource/images/diy/block_placeholder.png");a((()=>{B(),F()}));const B=()=>{const e=uni.getStorageSync("niucrowd_search_history")||[];S.value=e.slice(0,10)},F=async()=>{},M=async()=>{const e=j.value.trim();if(e){w.value=!0;try{await new Promise((e=>setTimeout(e,1e3))),b.value=[{project_id:1,project_name:`包含"${e}"的智能手表项目`,description:"全新智能手表，健康监测新体验",cover_image:"",target_amount:1e5,current_amount:5e4},{project_id:2,project_name:`${e}相关的环保背包设计`,description:"可持续发展的环保材料制作",cover_image:"",target_amount:5e4,current_amount:3e4}],C.value=b.value.length,(e=>{if(!e.trim())return;let a=uni.getStorageSync("niucrowd_search_history")||[];a=a.filter((a=>a!==e)),a.unshift(e),a=a.slice(0,20),uni.setStorageSync("niucrowd_search_history",a),S.value=a.slice(0,10)})(e)}catch(a){console.error("搜索失败:",a),f({title:"搜索失败，请重试",icon:"none"})}finally{w.value=!1}}else f({title:"请输入搜索关键词",icon:"none"})},T=()=>{j.value="",b.value=[],C.value=0},V=()=>{p({title:"提示",content:"确定要清空搜索历史吗？",success:e=>{e.confirm&&(uni.removeStorageSync("niucrowd_search_history"),S.value=[])}})},E=e=>{if(!j.value||!e)return e;const a=j.value.trim(),s=new RegExp(`(${a})`,"gi");return e.replace(s,'<span style="color: #FF6B6B;">$1</span>')};return(e,a)=>{const f=v,p=m,k=c,B=g;return s(),t(k,{class:"search-page"},{default:l((()=>[r(" 搜索框 "),u(k,{class:"search-header"},{default:l((()=>[u(k,{class:"search-box"},{default:l((()=>[u(k,{class:"search-input-wrapper"},{default:l((()=>[u(f,{class:"iconfont iconsousuo search-icon"}),u(p,{modelValue:j.value,"onUpdate:modelValue":a[0]||(a[0]=e=>j.value=e),placeholder:"搜索项目名称、关键词",class:"search-input","confirm-type":"search",onConfirm:M,focus:""},null,8,["modelValue"]),j.value?(s(),t(f,{key:0,class:"clear-btn",onClick:T},{default:l((()=>[u(f,{class:"iconfont iconguanbi"})])),_:1})):r("v-if",!0)])),_:1}),u(k,{class:"search-btn",onClick:M},{default:l((()=>[o("搜索")])),_:1})])),_:1})])),_:1}),r(" 搜索历史 "),!j.value&&S.value.length>0?(s(),t(k,{key:0,class:"search-history"},{default:l((()=>[u(k,{class:"section-header"},{default:l((()=>[u(f,{class:"section-title"},{default:l((()=>[o("搜索历史")])),_:1}),u(f,{class:"clear-history",onClick:V},{default:l((()=>[o("清空")])),_:1})])),_:1}),u(k,{class:"history-tags"},{default:l((()=>[(s(!0),n(d,null,i(S.value,((e,a)=>(s(),t(k,{key:a,class:"history-tag",onClick:a=>{return s=e,j.value=s,void M();var s}},{default:l((()=>[o(_(e),1)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})):r("v-if",!0),r(" 热门搜索 "),j.value?r("v-if",!0):(s(),t(k,{key:1,class:"hot-search"},{default:l((()=>[u(k,{class:"section-header"},{default:l((()=>[u(f,{class:"section-title"},{default:l((()=>[o("热门搜索")])),_:1})])),_:1}),u(k,{class:"hot-tags"},{default:l((()=>[(s(!0),n(d,null,i(x.value,((e,a)=>(s(),t(k,{key:a,class:"hot-tag",onClick:a=>{return s=e,j.value=s,void M();var s}},{default:l((()=>[o(_(e),1)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})),r(" 搜索结果 "),j.value?(s(),t(k,{key:2,class:"search-results"},{default:l((()=>[w.value?(s(),t(k,{key:0,class:"loading-wrapper"},{default:l((()=>[u(f,{class:"loading-text"},{default:l((()=>[o("搜索中...")])),_:1})])),_:1})):0===b.value.length?(s(),t(k,{key:1,class:"empty-wrapper"},{default:l((()=>[u(k,{class:"empty-icon"},{default:l((()=>[u(f,{class:"iconfont iconwushuju"})])),_:1}),u(f,{class:"empty-text"},{default:l((()=>[o("未找到相关项目")])),_:1}),u(f,{class:"empty-tip"},{default:l((()=>[o("试试其他关键词吧")])),_:1})])),_:1})):(s(),t(k,{key:2,class:"result-list"},{default:l((()=>[u(k,{class:"result-header"},{default:l((()=>[u(f,{class:"result-count"},{default:l((()=>[o("找到 "+_(C.value)+" 个相关项目",1)])),_:1})])),_:1}),(s(!0),n(d,null,i(b.value,(e=>(s(),t(k,{key:e.project_id,class:"project-item",onClick:a=>{return s=e.project_id,void y({url:`/addon/niucrowd/pages/project/detail?id=${s}`});var s}},{default:l((()=>[u(k,{class:"project-image"},{default:l((()=>[u(B,{src:e.cover_image||$.value,mode:"aspectFill",class:"cover-img"},null,8,["src"])])),_:2},1024),u(k,{class:"project-info"},{default:l((()=>[u(k,{class:"project-name",innerHTML:E(e.project_name)},null,8,["innerHTML"]),u(k,{class:"project-desc"},{default:l((()=>[o(_(e.description),1)])),_:2},1024),u(k,{class:"project-stats"},{default:l((()=>[u(k,{class:"stat-item"},{default:l((()=>[u(f,{class:"stat-label"},{default:l((()=>[o("目标：")])),_:1}),u(f,{class:"stat-value"},{default:l((()=>[o("¥"+_(e.target_amount),1)])),_:2},1024)])),_:2},1024),u(k,{class:"stat-item"},{default:l((()=>[u(f,{class:"stat-label"},{default:l((()=>[o("已筹：")])),_:1}),u(f,{class:"stat-value primary"},{default:l((()=>[o("¥"+_(e.current_amount),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),u(k,{class:"progress-bar"},{default:l((()=>[u(k,{class:"progress-fill",style:h({width:Math.min(e.current_amount/e.target_amount*100,100)+"%"})},null,8,["style"])])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1}))])),_:1})):r("v-if",!0)])),_:1})}}},[["__scopeId","data-v-eb3e42b4"]]);export{j as default};
