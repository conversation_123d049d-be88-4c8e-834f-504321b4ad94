import{_ as e}from"./u-icon.52b2e8b4.js";import{a4 as l,a5 as t,a6 as s,a8 as a,aN as i,i as c,j as o,o as r,c as n,w as u,b as d,T as y,$ as _,g as p,n as f,A as g,B as m,k as b,I as k}from"./index-12d8dd28.js";import{_ as S}from"./u-line.10a9c4d6.js";import{_ as h}from"./_plugin-vue_export-helper.1b428a4d.js";const v=h({name:"u-cell",data:()=>({}),mixins:[t,s,{props:{title:{type:[String,Number],default:()=>l.cell.title},label:{type:[String,Number],default:()=>l.cell.label},value:{type:[String,Number],default:()=>l.cell.value},icon:{type:String,default:()=>l.cell.icon},disabled:{type:Boolean,default:()=>l.cell.disabled},border:{type:Boolean,default:()=>l.cell.border},center:{type:Boolean,default:()=>l.cell.center},url:{type:String,default:()=>l.cell.url},linkType:{type:String,default:()=>l.cell.linkType},clickable:{type:Boolean,default:()=>l.cell.clickable},isLink:{type:Boolean,default:()=>l.cell.isLink},required:{type:Boolean,default:()=>l.cell.required},rightIcon:{type:String,default:()=>l.cell.rightIcon},arrowDirection:{type:String,default:()=>l.cell.arrowDirection},iconStyle:{type:[Object,String],default:()=>l.cell.iconStyle},rightIconStyle:{type:[Object,String],default:()=>l.cell.rightIconStyle},titleStyle:{type:[Object,String],default:()=>l.cell.titleStyle},size:{type:String,default:()=>l.cell.size},stop:{type:Boolean,default:()=>l.cell.stop},name:{type:[Number,String],default:()=>l.cell.name}}}],computed:{titleTextStyle(){return a(this.titleStyle)}},emits:["click"],methods:{addStyle:a,testEmpty:i.empty,clickHandler(e){this.disabled||(this.$emit("click",{name:this.name}),this.openPage(),this.stop&&this.preventEvent(e))}}},[["render",function(l,t,s,a,i,h){const v=c(o("u-icon"),e),$=b,z=k,w=c(o("u-line"),S);return r(),n($,{class:y(["u-cell",[l.customClass]]),style:f([h.addStyle(l.customStyle)]),"hover-class":l.disabled||!l.clickable&&!l.isLink?"":"u-cell--clickable","hover-stay-time":250,onClick:h.clickHandler},{default:u((()=>[d($,{class:y(["u-cell__body",[l.center&&"u-cell--center","large"===l.size&&"u-cell__body--large"]])},{default:u((()=>[d($,{class:"u-cell__body__content"},{default:u((()=>[l.$slots.icon||l.icon?(r(),n($,{key:0,class:"u-cell__left-icon-wrap"},{default:u((()=>[l.$slots.icon?_(l.$slots,"icon",{key:0},void 0,!0):(r(),n(v,{key:1,name:l.icon,"custom-style":l.iconStyle,size:"large"===l.size?22:18},null,8,["name","custom-style","size"]))])),_:3})):p("v-if",!0),d($,{class:"u-cell__title"},{default:u((()=>[p(" 将slot与默认内容用if/else分开主要是因为微信小程序不支持slot嵌套传递，这样才能解决collapse组件的slot不失效问题，label暂时未用到。 "),l.$slots.title||!l.title?_(l.$slots,"title",{key:0},void 0,!0):(r(),n(z,{key:1,class:y(["u-cell__title-text",[l.disabled&&"u-cell--disabled","large"===l.size&&"u-cell__title-text--large"]]),style:f([h.titleTextStyle])},{default:u((()=>[g(m(l.title),1)])),_:1},8,["style","class"])),_(l.$slots,"label",{},(()=>[l.label?(r(),n(z,{key:0,class:y(["u-cell__label",[l.disabled&&"u-cell--disabled","large"===l.size&&"u-cell__label--large"]])},{default:u((()=>[g(m(l.label),1)])),_:1},8,["class"])):p("v-if",!0)]),!0)])),_:3})])),_:3}),_(l.$slots,"value",{},(()=>[h.testEmpty(l.value)?p("v-if",!0):(r(),n(z,{key:0,class:y(["u-cell__value",[l.disabled&&"u-cell--disabled","large"===l.size&&"u-cell__value--large"]])},{default:u((()=>[g(m(l.value),1)])),_:1},8,["class"]))]),!0),l.$slots["right-icon"]||l.isLink?(r(),n($,{key:0,class:y(["u-cell__right-icon-wrap",[`u-cell__right-icon-wrap--${l.arrowDirection}`]])},{default:u((()=>[l.rightIcon&&!l.$slots["right-icon"]?(r(),n(v,{key:0,name:l.rightIcon,"custom-style":l.rightIconStyle,color:l.disabled?"#c8c9cc":"info",size:"large"===l.size?18:16},null,8,["name","custom-style","color","size"])):_(l.$slots,"right-icon",{key:1},void 0,!0)])),_:3},8,["class"])):p("v-if",!0),l.$slots.righticon?(r(),n($,{key:1,class:y(["u-cell__right-icon-wrap",[`u-cell__right-icon-wrap--${l.arrowDirection}`]])},{default:u((()=>[_(l.$slots,"righticon",{},void 0,!0)])),_:3},8,["class"])):p("v-if",!0)])),_:3},8,["class"]),l.border?(r(),n(w,{key:0})):p("v-if",!0)])),_:3},8,["class","style","hover-class","onClick"])}],["__scopeId","data-v-24694e79"]]);const $=h({name:"u-cell-group",mixins:[t,s,{props:{title:{type:String,default:()=>l.cellGroup.title},border:{type:Boolean,default:()=>l.cellGroup.border}}}],methods:{addStyle:a}},[["render",function(e,l,t,s,a,i){const h=k,v=b,$=c(o("u-line"),S);return r(),n(v,{style:f([i.addStyle(e.customStyle)]),class:y([[e.customClass],"u-cell-group"])},{default:u((()=>[e.title?(r(),n(v,{key:0,class:"u-cell-group__title"},{default:u((()=>[_(e.$slots,"title",{},(()=>[d(h,{class:"u-cell-group__title__text"},{default:u((()=>[g(m(e.title),1)])),_:1})]),!0)])),_:3})):p("v-if",!0),d(v,{class:"u-cell-group__wrapper"},{default:u((()=>[e.border?(r(),n($,{key:0})):p("v-if",!0),_(e.$slots,"default",{},void 0,!0)])),_:3})])),_:3},8,["style","class"])}],["__scopeId","data-v-c8de4fdc"]]);export{v as _,$ as a};
