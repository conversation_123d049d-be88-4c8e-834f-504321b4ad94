import{_ as e}from"./top-tabbar.d86f4f04.js";import{d as a,r as t,s as r,o as s,c as o,w as l,b as i,e as f,A as p,B as n,g as d,n as c,a as u,i as x,j as m,I as _,k as b,H as v,F as g}from"./index-12d8dd28.js";import{g as y}from"./diy_form.03332942.js";import{t as k}from"./topTabbar.16728c6f.js";import{_ as h}from"./_plugin-vue_export-helper.1b428a4d.js";import"./manifest.ed582bbb.js";const j=h(a({__name:"diy_form_result",setup(a){const h=t(null),j=t(0),C=t(0);let w=k().setTopTabbarParam({title:"",isBack:!1});r((e=>{j.value=e.record_id||0,C.value=e.form_id||0,T()}));const T=()=>{y({record_id:j.value}).then((e=>{h.value=e.data}))},B=()=>{u({url:"/app/pages/index/index",mode:"reLaunch"})},F=()=>{u({url:"/app/pages/index/diy_form",param:{form_id:C.value},mode:"redirectTo"})};return(a,t)=>{const r=x(m("top-tabbar"),e),y=_,k=b,C=v;return s(),o(k,{style:c(a.themeColor())},{default:l((()=>[h.value?(s(),o(k,{key:0,class:"w-screen h-screen flex flex-col items-center"},{default:l((()=>[i(r,{ref:"topTabbarRef",data:f(w)},null,8,["data"]),i(k,{class:"flex-1 flex flex-col items-center w-full pt-[180rpx]"},{default:l((()=>[i(y,{class:"nc-iconfont nc-icon-duihaoV6mm text-[#06ae56] mb-[30rpx] !text-[65rpx]"}),i(k,{class:"px-[30rpx] text-center leading-[1.3] text-[42rpx] font-bold mb-[30rpx]"},{default:l((()=>[p(n("default"==h.value.submitConfig.tips_type?"填写成功":h.value.submitConfig.tips_text),1)])),_:1}),i(k,{class:"text-[32rpx] mt-[32rpx] text-[#576b95]",onClick:t[0]||(t[0]=e=>{u({url:"/app/pages/index/diy_form_detail",param:{record_id:j.value},mode:"redirectTo"})})},{default:l((()=>[p(n(f(g)("diyForm.viewFillingDetails")),1)])),_:1})])),_:1}),i(k,{class:"pb-[260rpx] action-wrap"},{default:l((()=>[h.value.submitConfig.success_after_action.finish?(s(),o(C,{key:0,class:"w-[380rpx] !border-0 h-[80rpx] text-[28rpx] !text-[#ffffff] !bg-[#20bf64] flex-center font-500 rounded-[6rpx]",plain:!0,onClick:B},{default:l((()=>[p(n(f(g)("complete")),1)])),_:1})):d("v-if",!0),h.value.submitConfig.success_after_action.goBack?(s(),o(C,{key:1,class:"w-[380rpx] !border-0 h-[80rpx] text-[28rpx] text-[#333] !bg-[#f2f2f2] flex-center font-500 rounded-[6rpx]",plain:!0,onClick:F},{default:l((()=>[p(n(f(g)("diyForm.back")),1)])),_:1})):d("v-if",!0)])),_:1})])),_:1})):d("v-if",!0)])),_:1},8,["style"])}}}),[["__scopeId","data-v-5a838e69"]]);export{j as default};
