import{_ as e}from"./loading-page.vue_vue_type_script_setup_true_lang.1c76425e.js";import{d as l,p as a,r as t,z as s,x as r,l as u,bV as n,bW as o,aW as x,o as i,c,w as p,b as f,e as v,n as d,g as _,R as g,a3 as m,S as b,A as h,i as y,j as w,k,G as j,aI as C,ap as F,I as T,E as z,T as E,B as I,a as X,a_ as B,aH as S}from"./index-12d8dd28.js";import{t as W}from"./topTabbar.16728c6f.js";import{_ as A}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-loading-icon.85553345.js";import"./u-transition.5e23e77b.js";/* empty css                                                                     */const G=A(l({__name:"level",setup(l){const A=a(),G=t(!1),H=t([]),O=t([]),P=t(0),R=t(0);W().setTopTabbarParam({title:"会员等级"}),t({title:"会员等级",topStatusBar:{style:"style-1",bgColor:"#fff",rollBgColor:"#333",textColor:"#333",rollTextColor:"#333"}}),s((()=>{r()&&(L(),K())}));const V=u((()=>A.info)),$=e=>{let l=e,a=100;return H.value[l]&&H.value[l].growth&&(a=V.value.growth/H.value[l].growth*100),a},q=t(""),D=t(""),J=t(""),K=()=>{G.value=!0,n().then((e=>{H.value=e.data||[];let l=!0;V.value&&H.value&&H.value.length&&H.value.forEach(((e,a)=>{e.level_id==V.value.member_level&&(l=!1,P.value=a,R.value=P.value,Q(a))})),l&&Q(0),H.value&&H.value.length>=5?(q.value="width:115rpx;",D.value="max-width:115rpx;",J.value="width:460rpx;transform: translateX(-235rpx);"):H.value&&4==H.value.length?(q.value="width:144rpx;",D.value="max-width:144rpx;",J.value="width:436rpx;transform: translateX(-218rpx);"):H.value&&3==H.value.length?(q.value="width:192rpx;",D.value="max-width:192rpx;",J.value="width:388rpx;transform: translateX(-194rpx);"):H.value&&2==H.value.length?(q.value="width:289rpx;",D.value="max-width:289rpx;",J.value="width:289rpx;transform: translateX(-144rpx);"):(D.value="max-width:578rpx;",q.value="width:100%;"),G.value=!1})).catch((()=>{G.value=!1}))},L=()=>{o().then((e=>{O.value=e.data}))},M=e=>{P.value=e.detail.current,R.value=P.value,Q(e.detail.current)},N=t({}),Q=e=>{let l=x(H.value[e]);if(l&&l.level_benefits&&(l.benefits_arr=[],Object.values(l.level_benefits).forEach(((e,a,t)=>{e.content&&l.benefits_arr.push(e.content)}))),l&&l.level_gifts){l.gifts_arr=[];for(let e in l.level_gifts)l.level_gifts[e].content&&(l.level_gifts[e].content.forEach(((l,a,t)=>{t[a].type=e})),l.gifts_arr=l.gifts_arr.concat(l.level_gifts[e].content))}N.value=l};return(l,a)=>{const t=y(w("loading-page"),e),s=k,r=j,u=B,n=S,o=C,x=F,W=T;return i(),c(s,{style:d(l.themeColor()),class:"bg-[var(--page-bg-color)] min-h-[100vh] overflow-hidden"},{default:p((()=>[f(t,{loading:G.value&&v(V)},null,8,["loading"]),!G.value&&v(V)&&H.value&&H.value.length?(i(),c(s,{key:0,class:"min-h-[100vh] overflow-hidden flex flex-col",style:d({backgroundColor:N.value.level_style.bg_color})},{default:p((()=>[f(s,null,{default:p((()=>[f(s,{class:"pt-[40rpx] mb-[40rpx]"},{default:p((()=>[_(" 轮播图 "),f(s,{class:"relative"},{default:p((()=>[f(o,{class:"swiper ns-indicator-dots relative",style:{height:"300rpx"},onChange:M,current:P.value,"previous-margin":"30rpx","next-margin":"30rpx"},{default:p((()=>[(i(!0),g(b,null,m(H.value,((e,l)=>(i(),c(n,{class:"swiper-item",key:e.id},{default:p((()=>[f(s,{class:"h-[300rpx] relative"},{default:p((()=>[v(V).member_level==e.level_id&&P.value==l?(i(),c(s,{key:0,class:"text-[24rpx] absolute top-0 left-0 z-10 h-[66rpx] !bg-contain w-[150rpx] flex pt-[12rpx] pl-[16rpx] box-border",style:d({background:"url("+v(z)(N.value.level_tag)+") no-repeat",color:N.value.level_style.level_color})},{default:p((()=>[h("当前等级")])),_:1},8,["style"])):_("v-if",!0),f(s,{class:E(["absolute top-0 left-0 right-0 bottom-0 z-20 px-[30rpx] pt-[68rpx] box-border",{"px-[50rpx]":P.value!=l}])},{default:p((()=>[f(s,{class:"flex items-center leading-[50rpx] mb-[90rpx]"},{default:p((()=>[f(r,{class:"h-[32rpx] w-[34rpx] align-middle",src:v(z)(e.level_icon?e.level_icon:""),mode:"aspectFill"},null,8,["src"]),f(s,{class:"text-[36rpx] font-bold ml-[10rpx] max-w-[340rpx] truncate",style:d({color:N.value.level_style.level_color})},{default:p((()=>[h(I(e.level_name),1)])),_:2},1032,["style"])])),_:2},1024),f(s,{class:"flex items-baseline",style:d({color:N.value.level_style.level_color})},{default:p((()=>[f(s,{class:"text-[30rpx] font-500 leading-[38rpx]"},{default:p((()=>[h(I(v(V).growth),1)])),_:1}),f(s,{class:"text-[24rpx] leading-[34rpx]"},{default:p((()=>[h("/"+I(H.value[l].growth)+"成长值",1)])),_:2},1024)])),_:2},1032,["style"]),f(s,{class:"flex justify-between items-center mt-[10rpx]"},{default:p((()=>[f(s,{class:"flex flex-col flex-1"},{default:p((()=>[f(s,null,{default:p((()=>[f(u,{percent:$(l),"border-radius":100,activeColor:N.value.level_style.level_color,backgroundColor:"#fff","stroke-width":"4"},null,8,["percent","activeColor"])])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["class"]),f(s,{class:"relatvie h-full w-full"},{default:p((()=>[f(r,{class:E(["h-full w-full",{"swiper-animation":P.value!=l}]),src:v(z)(e.level_bg),"show-menu-by-longpress":!0},null,8,["src","class"])])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1},8,["current"])])),_:1})])),_:1}),f(s,{class:"mb-[30rpx] relative"},{default:p((()=>[f(s,{class:"bg-[#fff] opacity-15 h-[2rpx] w-full absolute top-[15rpx]"}),f(s,{style:d(J.value),class:"bg-[#fff] opacity-60 h-[2rpx] absolute top-[15rpx] z-4 left-[50%]"},null,8,["style"]),f(s,{class:"mx-[86rpx]"},{default:p((()=>[f(x,{"scroll-x":!0,"scroll-with-animation":"","scroll-into-view":"id"+(R.value?R.value-1:0)},{default:p((()=>[f(s,{class:"flex flex-nowrap py-[10rpx]"},{default:p((()=>[(i(!0),g(b,null,m(H.value,((e,l)=>(i(),c(s,{key:e.id,style:d(q.value),class:"flex-shrink-0 flex flex-col items-center justify-center",onClick:e=>(e=>{R.value=e,P.value=e,Q(e)})(l),id:"id"+l},{default:p((()=>[f(s,{class:E(["w-[14rpx] h-[14rpx] level-class",{"level-select":R.value==l}])},null,8,["class"]),f(s,{style:d(D.value),class:E(["text-[22rpx] text-[#aaa] mt-[16rpx] truncate",{"!text-[#fff]":R.value==l}])},{default:p((()=>[h(I(e.level_name),1)])),_:2},1032,["style","class"])])),_:2},1032,["style","onClick","id"])))),128))])),_:1})])),_:1},8,["scroll-into-view"])])),_:1})])),_:1}),N.value.benefits_arr&&N.value.benefits_arr.length?(i(),c(s,{key:0,class:"flex mx-[var(--sidebar-m)] pt-[30rpx] pb-[46rpx] items-center flex-col level_benefits",style:d({backgroundImage:"url("+v(z)(N.value.member_bg)+")"})},{default:p((()=>[f(s,{class:"flex items-center justify-center"},{default:p((()=>[f(W,{class:"text-[#fff] text-[30rpx] font-500 leading-[44rpx]"},{default:p((()=>[h("会员权益")])),_:1})])),_:1}),f(s,{class:"flex flex-wrap w-[690rpx] mt-[40rpx] justify-between"},{default:p((()=>[(i(!0),g(b,null,m(N.value.benefits_arr,((e,l)=>(i(),c(s,{class:"flex flex-col w-[25%] items-center",key:l},{default:p((()=>[f(r,{class:"h-[88rpx] w-[88rpx]",src:v(z)(e.icon),mode:"heightFix"},null,8,["src"]),f(W,{class:"text-[rgba(255,255,255,0.9)] mt-[16rpx] text-[24rpx] leading-[34rpx]"},{default:p((()=>[h(I(e.title),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1},8,["style"])):_("v-if",!0)])),_:1}),f(s,{class:"flex-1 rounded-t-[40rpx] px-[30rpx] pt-[var(--pad-top-m)] mt-[-10rpx] relative tab-bar",style:d({background:`linear-gradient( 180deg, ${N.value.level_style.gift} 0%, #FFFFFF 20%)`})},{default:p((()=>[_(" 升级礼包 "),N.value.gifts_arr&&N.value.gifts_arr.length?(i(),c(s,{key:0},{default:p((()=>[f(s,{class:"pb-[30rpx] flex items-center"},{default:p((()=>[f(W,{class:"text-[30rpx] text-[#333] font-500 leading-[44rpx]"},{default:p((()=>[h("升级礼包")])),_:1})])),_:1}),f(s,{class:"flex flex-wrap"},{default:p((()=>[(i(!0),g(b,null,m(N.value.gifts_arr,((e,l)=>(i(),c(s,{key:l,class:E(["mb-[20rpx]",{"mr-[21rpx]":(l+1)%3!=0}])},{default:p((()=>[f(s,{class:"relative box-border mb-[16rpx] w-[216rpx] h-[180rpx] !bg-contain",style:d({background:"url("+v(z)(e.background)+") no-repeat"})},null,8,["style"]),f(s,{class:"text-center font-500 text-[#333] text-[28rpx] truncate leading-[40rpx] max-w-[216rpx]"},{default:p((()=>[h(I(e.text),1)])),_:2},1024)])),_:2},1032,["class"])))),128))])),_:1})])),_:1})):_("v-if",!0),_(" 升级技巧 "),O.value&&O.value.length?(i(),c(s,{key:1},{default:p((()=>[f(s,{class:"pt-[30rpx] pb-[30rpx] flex items-center"},{default:p((()=>[f(W,{class:"text-[30rpx] text-[#333] font-500 leading-[44rpx]"},{default:p((()=>[h("升级技巧")])),_:1})])),_:1}),f(s,{class:"pb-[30rpx]"},{default:p((()=>[(i(!0),g(b,null,m(O.value,((e,l)=>(i(),c(s,{class:"flex items-center mb-[34rpx]",key:l},{default:p((()=>[f(r,{class:"h-[100rpx] w-[100rpx] mr-[20rpx]",src:v(z)(e.icon),mode:"heightFix"},null,8,["src"]),f(s,{class:"flex flex-col"},{default:p((()=>[f(s,{class:"text-[28rpx] leading-[38rpx] mb-[8rpx]"},{default:p((()=>[h(I(e.title),1)])),_:2},1024),f(s,{class:"text-[24rpx] text-[var(--text-color-light9)] leading-[34rpx]"},{default:p((()=>[h(I(e.desc),1)])),_:2},1024)])),_:2},1024),f(W,{class:"skill-btn",onClick:l=>v(X)({url:e.button.wap_redirect,param:{},mode:"redirectTo"})},{default:p((()=>[h(I(e.button.text),1)])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1})])),_:1})):_("v-if",!0)])),_:1},8,["style"])])),_:1},8,["style"])):_("v-if",!0),G.value||H.value&&H.value.length?_("v-if",!0):(i(),c(s,{key:1,class:""},{default:p((()=>[f(s,{class:"empty-page"},{default:p((()=>[f(r,{class:"img",src:v(z)("static/resource/images/empty.png"),mode:"aspectFill"},null,8,["src"]),f(W,{class:"desc"},{default:p((()=>[h("暂无会员等级")])),_:1})])),_:1})])),_:1}))])),_:1},8,["style"])}}}),[["__scopeId","data-v-a2bdf418"]]);export{G as default};
