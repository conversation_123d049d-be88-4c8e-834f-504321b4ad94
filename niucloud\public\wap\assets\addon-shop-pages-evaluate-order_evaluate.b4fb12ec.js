import{d as e,l as a,i as t,j as l,o as r,c as o,w as s,R as u,S as n,a3 as d,e as i,T as p,b as c,E as x,C as m,f as _,A as f,B as v,v as g,y as h,ax as b,aw as y,I as j,k as w,r as V,s as C,M as k,a as z,n as F,H as U,g as A,ay as E}from"./index-12d8dd28.js";import{_ as I}from"./u-icon.52b2e8b4.js";import{_ as B}from"./u--image.376d9df2.js";import{s as q,_ as M}from"./evaluate.e124a618.js";import{_ as P}from"./u-tabbar.82e98c18.js";import{_ as R}from"./loading-page.vue_vue_type_script_setup_true_lang.1c76425e.js";import{g as S}from"./order.5fba8f66.js";import{_ as T}from"./u-upload.837ab45e.js";import{_ as H}from"./_plugin-vue_export-helper.1b428a4d.js";/* empty css                                                               */import"./u-image.f86d3186.js";import"./u-transition.5e23e77b.js";/* empty css                                                                     *//* empty css                                                                */import"./u-safe-bottom.a6f7ba25.js";import"./u-loading-icon.85553345.js";const J=e({__name:"upload-img",props:{modelValue:{type:String||Array},maxCount:{type:Number,default:9},multiple:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(e,{emit:V}){const C=e,k=a({get:()=>C.modelValue,set(e){V("update:modelValue",e)}}),z=a((()=>C.maxCount)),F=e=>{C.multiple?e.file.forEach((e=>{U({file:e})})):U(e)},U=e=>{var a;if((null==(a=k.value)?void 0:a.length)>=z.value)return h({title:`最多允许上传${z.value}张图片`,icon:"none"}),!1;b({filePath:e.file.url,name:"file"}).then((e=>{var a;(null==(a=k.value)?void 0:a.length)<z.value&&k.value.push(e.data.url)})).catch((()=>{}))};return(e,a)=>{const h=t(l("u-icon"),I),b=t(l("u--image"),B),V=j,U=w,A=t(l("u-upload"),T);return r(),o(U,{class:"flex flex-wrap"},{default:s((()=>[(r(!0),u(n,null,d(i(k),((e,a)=>(r(),o(U,{class:p(["mb-[18rpx] relative",{"mr-[18rpx]":(a+1)%4!=0}])},{default:s((()=>[c(b,{class:"rounded-[10rpx] overflow-hidden",width:"140rpx",height:"140rpx",src:i(x)(e||""),model:"aspectFill",onClick:a=>(e=>{if(""===e)return!1;var a=[];a.push(x(e)),y({indicator:"number",loop:!0,urls:a})})(e)},{error:s((()=>[c(h,{name:"photo",color:"#999",size:"50"})])),_:2},1032,["src","onClick"]),c(U,{class:"absolute top-0 right-[0] bg-[#373737] flex justify-end h-[28rpx] w-[28rpx] rounded-bl-[40rpx]",onClick:m((e=>(e=>{k.value.splice(e,1)})(a)),["stop"])},{default:s((()=>[c(V,{class:"nc-iconfont nc-icon-guanbiV6xx !text-[20rpx] mt-[2rpx] mr-[2rpx] text-[#fff]"})])),_:2},1032,["onClick"])])),_:2},1032,["class"])))),256)),_(c(U,{class:"w-[140rpx] h-[140rpx]"},{default:s((()=>[c(A,{onAfterRead:F,maxCount:i(z),multiple:C.multiple},{default:s((()=>[c(U,{class:"flex items-center justify-center w-[140rpx] h-[140rpx] border-[2rpx] border-dashed border-[#ddd] text-center text-[var(--text-color-light9)] rounded-[var(--goods-rounded-big)]"},{default:s((()=>[c(U,null,{default:s((()=>[c(U,{class:"nc-iconfont nc-icon-xiangjiV6xx text-[50rpx]"}),c(U,{class:"text-[24rpx] mt-[12rpx]"},{default:s((()=>[f(v(i(k).length)+"/"+v(i(z)),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["maxCount","multiple"])])),_:1},512),[[g,i(k).length<i(z)]])])),_:1})}}}),N=H(e({__name:"order_evaluate",setup(e){const a=V([]),m=V([]),_=V("2"),g=V(!1),b=V("");V(null),C((e=>{if(e.order_id)b.value=e.order_id,y(b.value);else{k({url:"/addon/shop/pages/order/list",param:{status:5},title:"缺少订单id"})}}));const y=e=>{g.value=!0,S(e).then((e=>{if(e.data.is_evaluate)return N(b.value),!1;e.data.order_goods.forEach((e=>{1==e.status&&(a.value.push(e),m.value.push({order_id:e.order_id,order_goods_id:e.order_goods_id,goods_id:e.goods_id,content:"",images:[],scores:5}))})),g.value=!1})).catch((()=>{g.value=!1}))},T=()=>{_.value="1"===_.value?"2":"1"},H=()=>{if(m.value.some((e=>""==e.content)))return h({title:"请输入你的评价",icon:"none"}),!1;for(let e=0;e<m.value.length;e++){let a=m.value[e];a.content.length>200&&(a.content=a.content.substr(0,200))}m.value.forEach((e=>e.is_anonymous=_.value)),g.value=!0,q({evaluate_array:m.value}).then((e=>{g.value=!1,N(b.value)})).catch((()=>{g.value=!1}))},N=e=>{z({url:"/addon/shop/pages/evaluate/order_evaluate_view",param:{order_id:e},mode:"redirectTo"})};return(e,h)=>{const b=t(l("u-icon"),I),y=t(l("u--image"),B),V=w,C=j,k=t(l("u-rate"),M),z=E,q=U,S=t(l("u-tabbar"),P),N=t(l("loading-page"),R);return r(),o(V,{class:"bg-[var(--page-bg-color)] min-h-screen",style:F(e.themeColor())},{default:s((()=>[c(V,{class:"px-[var(--sidebar-m)] py-[var(--top-m)]"},{default:s((()=>[(r(!0),u(n,null,d(a.value,((e,a)=>(r(),o(V,{key:a,class:"card-template mb-[var(--top-m)]"},{default:s((()=>[c(V,{class:"bg-[var(--temp-bg)] p-[20rpx] rounded-[var(--rounded-mid)] flex"},{default:s((()=>[c(y,{radius:"var(--goods-rounded-mid)",width:"150rpx",height:"150rpx",src:i(x)(e.goods_image_thumb_small?e.goods_image_thumb_small:""),model:"aspectFill"},{error:s((()=>[c(b,{name:"photo",color:"#999",size:"50"})])),_:2},1032,["src"]),c(V,{class:"flex-1 flex flex-wrap ml-[20rpx] my-[4rpx]"},{default:s((()=>[c(V,null,{default:s((()=>[c(V,{class:"text-[26rpx] leading-[40rpx] max-w-[450rpx] truncate"},{default:s((()=>[f(v(e.goods_name),1)])),_:2},1024),e.sku_name?(r(),o(V,{key:0,class:"max-w-[450rpx] mt-[14rpx] truncate text-[22rpx] text-[var(--text-color-light9)] leading-[28rpx]"},{default:s((()=>[f(v(e.sku_name),1)])),_:2},1024)):A("v-if",!0)])),_:2},1024),c(V,{class:"mt-auto w-full flex justify-between items-center"},{default:s((()=>[c(V,{class:"flex items-baseline price-font"},{default:s((()=>[c(C,{class:"text-[24rpx] font-500"},{default:s((()=>[f("￥")])),_:1}),c(C,{class:"text-[40rpx] font-500"},{default:s((()=>[f(v(parseFloat(e.price).toFixed(2).split(".")[0]),1)])),_:2},1024),c(C,{class:"text-[24rpx] font-500"},{default:s((()=>[f("."+v(parseFloat(e.price).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),c(C,{class:"font-400 text-[28rpx] text-[#333]"},{default:s((()=>[f("x"+v(e.num),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),c(V,{class:"flex items-center mt-[30rpx]"},{default:s((()=>[c(k,{count:5,modelValue:m.value[a].scores,"onUpdate:modelValue":e=>m.value[a].scores=e,"active-color":"var(--primary-color)",size:"36rpx",gutter:"1"},null,8,["modelValue","onUpdate:modelValue"]),c(C,{class:"ml-[16rpx] text-[28rpx] pt-[2rpx] text-[var(--primary-color)]"},{default:s((()=>[f(v(1===m.value[a].scores?"差评":2===m.value[a].scores||3===m.value[a].scores?"中评":"好评"),1)])),_:2},1024)])),_:2},1024),c(z,{class:"!text-[26rpx] px-[2rpx] mt-[16rpx] w-[100%] !text-[#333] !leading-[1.5]",modelValue:m.value[a].content,"onUpdate:modelValue":e=>m.value[a].content=e,modelModifiers:{trim:!0},placeholder:"请在此处输入你的评价",placeholderClass:"text-[26rpx] text-[var(--text-color-light9)]",maxlength:"200"},null,8,["modelValue","onUpdate:modelValue"]),A(' <view class="text-right text-[24rpx] text-[var(--text-color-light6)]">{{ form[index].content.length >= 200 ? 200 : form[index].content.length }}/200</view> '),c(i(J),{class:"mt-[20rpx]",modelValue:m.value[a].images,"onUpdate:modelValue":e=>m.value[a].images=e,"max-count":9,multiple:!0},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024)))),128))])),_:1}),c(S,{fixed:!0,placeholder:!0,safeAreaInsetBottom:!0,zIndex:"9999"},{default:s((()=>[c(V,{class:"flex items-center pl-[30rpx] pr-[20rpx] box-border justify-between w-[100%]"},{default:s((()=>[c(V,{class:"flex items-center",onClick:T},{default:s((()=>[c(C,{class:p(["iconfont text-color text-[30rpx] mr-[12rpx] text-[var(--text-color-light9)]",{"iconxuanze1 text-[var(--primary-color)]":"1"===_.value,"nc-iconfont nc-icon-yuanquanV6xx":"1"!==_.value}])},null,8,["class"]),c(C,{class:p(["text-[28rpx] leading-[34rpx]",{"text-[var(--primary-color)]":"1"===_.value,"text-[var(--text-color-light6)]":"1"!==_.value}])},{default:s((()=>[f("匿名")])),_:1},8,["class"])])),_:1}),c(q,{class:"!w-[240rpx] !h-[70rpx] text-[26rpx] !m-0 flex-center rounded-full text-white primary-btn-bg remove-border font-500","hover-class":"none",onClick:H},{default:s((()=>[f("提交")])),_:1})])),_:1})])),_:1}),c(N,{loading:g.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-e6068a62"]]);export{N as default};
