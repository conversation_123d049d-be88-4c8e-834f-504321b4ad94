import{d as e,r as a,l as s,a1 as t,N as l,s as r,o,c as n,w as d,g as i,b as c,e as p,n as u,R as x,a3 as _,S as f,A as m,B as g,C as v,ah as h,ai as y,D as b,G as k,k as w,ap as j,I as C,H as F,i as T,j as I,E,F as S,T as R,a as M,an as P,aq as z,b0 as $}from"./index-12d8dd28.js";import{_ as q}from"./u-popup.155f7cb9.js";import{a as A,b as G,c as H}from"./rank.4f838a63.js";import{M as N}from"./mescroll-body.76b124f6.js";import{u as U}from"./useMescroll.26ccf5de.js";import{M as W}from"./mescroll-empty.581da7ef.js";import{t as B}from"./topTabbar.16728c6f.js";import{u as D}from"./useGoods.854430c9.js";import{_ as O}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-transition.5e23e77b.js";/* empty css                                                                     */import"./u-icon.52b2e8b4.js";/* empty css                                                               */import"./u-safe-bottom.a6f7ba25.js";import"./mescroll-i18n.e418d218.js";const V=O(e({__name:"rank",setup(e){const O=D(),{mescrollInit:V,downCallback:X,getMescroll:J}=U(y,h),K=a(null),L=a(!1);let Q={};B().setTopTabbarParam({title:""});const Y=s((()=>b(Number(Q.height)+Q.top+8)+30+"rpx;")),Z=t(),ee=Z.windowHeight/Z.screenWidth*750,ae=s((()=>`${ee-450}rpx`)),se=a(!1),te=()=>{se.value=!1},le=a([]),re=a([]),oe=a(!1),ne=(e=!1)=>{A().then((a=>{if(le.value=a.data,ce.value){for(let e=0;e<le.value.length;e++)if(ce.value==le.value[e].rank_id){ue(le.value[e],e);break}}else e&&le.value&&le.value.length?ue(le.value[0],0):le.value.length||(L.value=!0);P((()=>{z().selectAll(".category-btn").boundingClientRect((e=>{if(e&&e.length>0){const a=$(20)*(e.length-1),s=e.reduce(((e,a)=>e+a.width),0)+a,l=t().windowWidth;oe.value=s<=.93*l}else console.error("Failed to get .category-btn elements.")})).exec()}))})).catch((e=>{console.error("加载分类数据失败",e)}))},de=l({}),ie=a(0),ce=a(0),pe=a();function ue(e,a){var s;L.value=!1,re.value=[],ie.value=a,ce.value=e.rank_id,pe.value=e.goods_source,null==(s=J())||s.resetUpScroll()}const xe=e=>{if(0==le.value.length)return;L.value=!1;let a={page:e.num,limit:e.size,rank_id:ce.value};H(a).then((a=>{let s=a.data.data.map((e=>e)),t=!0;1==e.num&&(re.value=[]),re.value=re.value.concat(s),t="goods"!=pe.value,e.endSuccess(s.length,t),L.value=!0})).catch((()=>{L.value=!0,e.endErr()}))};function _e(e){switch(e){case 1:return E("addon/shop/rank/rank_first.png");case 2:return E("addon/shop/rank/rank_second.png");case 3:return E("addon/shop/rank/rank_third.png");default:return E("addon/shop/rank/rank.png")}}return r((async e=>{ce.value=e.rank_id||0,G().then((e=>{Object.assign(de,e.data)})),ne(!0)})),(e,a)=>{const s=k,t=w,l=j,r=C,h=F,y=T(I("u-popup"),q);return o(),n(t,{class:"min-h-[100vh]",style:u(e.themeColor())},{default:d((()=>[i(" 顶部图片 "),c(t,{class:"rank-head"},{default:d((()=>[c(s,{class:"w-[100%] h-[435rpx]",src:p(E)(de.rank_images),mode:"aspectFill"},null,8,["src"]),c(t,{class:"content-box"},{default:d((()=>[i(" 榜单分类按钮 "),c(l,{"scroll-x":"true",class:"category-slider","scroll-with-animation":"","scroll-into-view":"id"+ie.value},{default:d((()=>[c(t,{class:"category-con",style:u({justifyContent:oe.value?"center":"flex-start"})},{default:d((()=>[(o(!0),x(f,null,_(le.value,((e,a)=>(o(),n(t,{class:"category-btn",key:a,id:"id"+a,onClick:s=>ue(e,a),style:u({color:ie.value===a?de.select_color:de.no_color,background:ie.value===a?`linear-gradient(to right, ${de.select_bg_color_start}, ${de.select_bg_color_end})`:"transparent"})},{default:d((()=>[c(t,null,{default:d((()=>[m(g(e.name),1)])),_:2},1024)])),_:2},1032,["id","onClick","style"])))),128))])),_:1},8,["style"])])),_:1},8,["scroll-into-view"]),i(' <view class="content">\r\n                  <text class="text-[26rpx]">{{rankConfig.rank_name}}</text>\r\n                </view> ')])),_:1}),de.rank_remark?(o(),n(t,{key:0,class:"side-tab",style:u({top:p(Y)}),onClick:a[0]||(a[0]=e=>se.value=!0)},{default:d((()=>[c(r,{class:"iconfont icona-paihangbangpc30 icon"}),c(r,{class:"desc"},{default:d((()=>[m(g(p(S)("rankingRules")),1)])),_:1})])),_:1},8,["style"])):i("v-if",!0)])),_:1}),c(t,{class:"rank-list p-[20rpx] relative -mt-[42rpx]"},{default:d((()=>[i(" 列表 "),c(N,{ref_key:"mescrollRef",ref:K,height:p(ae),onInit:p(V),down:{use:!1},onUp:xe},{default:d((()=>[re.value.length?(o(!0),x(f,{key:0},_(re.value,((e,a)=>(o(),n(t,{class:R(["bg-[#fff] flex rounded-[var(--rounded-mid)] p-[20rpx]",{"mb-[20rpx]":re.value.length-1!=a}]),key:e.goods_id,onClick:a=>{return s=e.goods_id,void M({url:"/addon/shop/pages/goods/detail",param:{goods_id:s}});var s}},{default:d((()=>[c(t,{class:"w-[240rpx] h-[240rpx] flex items-center justify-center relative"},{default:d((()=>[i(" 榜单排名图片 "),a<5?(o(),n(s,{key:0,class:"absolute top-[7rpx] left-[10rpx] w-[50rpx] h-[58rpx]",style:{zIndex:9},src:_e(e.rank_num),mode:"aspectFill"},null,8,["src"])):i("v-if",!0),a<5?(o(),n(t,{key:1,class:"absolute top-[15rpx] left-[10rpx] flex items-center justify-center w-[50rpx] h-[50rpx]",style:{zIndex:10}},{default:d((()=>[c(r,{class:"text-[24rpx] font-bold text-[#fff]"},{default:d((()=>[m(g(a+1),1)])),_:2},1024)])),_:2},1024)):i("v-if",!0),e.goods_cover_thumb_mid?(o(),n(s,{key:2,class:"w-[250rpx] h-[250rpx] rounded-[var(--rounded-mid)]",src:p(E)(e.goods_cover_thumb_mid),mode:"aspectFill",onError:a=>e.goods_cover_thumb_mid="static/resource/images/diy/shop_default.jpg"},null,8,["src","onError"])):(o(),n(s,{key:3,class:"w-[240rpx] h-[240rpx] rounded-[var(--rounded-mid)]",src:p(E)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"]))])),_:2},1024),c(t,{class:"flex flex-col flex-1 justify-between ml-[20rpx] pt-[4rpx]"},{default:d((()=>[c(t,{class:"text-[28rpx] text-[#333] leading-[40rpx] multi-hidden mb-[10rpx]"},{default:d((()=>[e.goods_brand?(o(),n(t,{key:0,class:"brand-tag",style:u(p(O).baseTagStyle(e.goods_brand))},{default:d((()=>[m(g(e.goods_brand.brand_name),1)])),_:2},1032,["style"])):i("v-if",!0),m(" "+g(e.goods_name),1)])),_:2},1024),e.goods_label_name&&e.goods_label_name.length?(o(),n(t,{key:0,class:"flex flex-wrap"},{default:d((()=>[(o(!0),x(f,null,_(e.goods_label_name,((e,a)=>(o(),x(f,null,["icon"==e.style_type&&e.icon?(o(),n(s,{key:0,class:"img-tag",src:p(E)(e.icon),mode:"heightFix",onError:a=>p(O).error(e,"icon")},null,8,["src","onError"])):"diy"!=e.style_type&&e.icon?i("v-if",!0):(o(),n(t,{key:1,class:"base-tag",style:u(p(O).baseTagStyle(e))},{default:d((()=>[m(g(e.label_name),1)])),_:2},1032,["style"]))],64)))),256))])),_:2},1024)):i("v-if",!0),c(t,{class:"flex items-center"},{default:d((()=>[c(t,{class:"text-[var(--price-text-color)] price-font flex items-baseline"},{default:d((()=>[c(r,{class:"text-[24rpx] font-500"},{default:d((()=>[m("￥")])),_:1}),c(r,{class:"text-[40rpx] font-500"},{default:d((()=>[m(g(p(O).goodsPrice(e).toFixed(2).split(".")[0]),1)])),_:2},1024),c(r,{class:"text-[24rpx] font-500"},{default:d((()=>[m("."+g(p(O).goodsPrice(e).toFixed(2).split(".")[1]),1)])),_:2},1024)])),_:2},1024),"member_price"==p(O).priceType(e)?(o(),n(s,{key:0,class:"max-w-[50rpx] h-[28rpx] ml-[6rpx]",src:p(E)("addon/shop/VIP.png"),mode:"heightFix"},null,8,["src"])):"newcomer_price"==p(O).priceType(e)?(o(),n(s,{key:1,class:"max-w-[60rpx] h-[28rpx] ml-[6rpx]",src:p(E)("addon/shop/newcomer.png"),mode:"heightFix"},null,8,["src"])):"discount_price"==p(O).priceType(e)?(o(),n(s,{key:2,class:"max-w-[80rpx] h-[28rpx] ml-[6rpx]",src:p(E)("addon/shop/discount.png"),mode:"heightFix"},null,8,["src"])):i("v-if",!0),c(t,{id:"itemCart"+a,class:"w-[102rpx] box-border ml-auto text-center text-[#fff] primary-btn-bg h-[46rpx] text-[22rpx] leading-[46rpx] rounded-[100rpx]"},{default:d((()=>[m("去购买")])),_:2},1032,["id"])])),_:2},1024)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128)):i("v-if",!0),!re.value.length&&L.value?(o(),n(W,{key:1,option:{tip:"暂无商品",btnText:"去逛逛"},onEmptyclick:a[1]||(a[1]=e=>p(M)({url:"/addon/shop/pages/goods/list"}))})):i("v-if",!0)])),_:1},8,["height","onInit"]),c(t,{onTouchmove:a[3]||(a[3]=v((()=>{}),["prevent","stop"]))},{default:d((()=>[c(y,{show:se.value,onClose:te,mode:"center",round:"var(--rounded-big)"},{default:d((()=>[c(t,{class:"w-[570rpx] px-[32rpx] popup-common center"},{default:d((()=>[c(t,{class:"title"},{default:d((()=>[m(g(p(S)("rankingRules")),1)])),_:1}),c(l,{"scroll-y":!0,class:"px-[30rpx] box-border max-h-[260rpx]"},{default:d((()=>[c(t,{class:"text-[28rpx] leading-[40rpx] mb-[20rpx]"},{default:d((()=>[m(g(de.rank_remark),1)])),_:1})])),_:1}),c(t,{class:"btn-wrap !pt-[40rpx]"},{default:d((()=>[c(h,{class:"primary-btn-bg w-[480rpx] h-[70rpx] text-[26rpx] leading-[70rpx] rounded-[35rpx] !text-[#fff] font-500",onClick:a[2]||(a[2]=e=>se.value=!1)},{default:d((()=>[m("我知道了")])),_:1})])),_:1})])),_:1})])),_:1},8,["show"])])),_:1})])),_:1})])),_:1},8,["style"])}}}),[["__scopeId","data-v-2b64f87b"]]);export{V as default};
