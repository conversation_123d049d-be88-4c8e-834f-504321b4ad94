import{d as e,l,r as a,p as t,m as r,s as o,x as s,y as u,a as i,t as n,N as p,Q as d,o as m,c,w as x,b as f,A as g,B as b,e as _,R as v,g as h,S as y,T as k,C as w,n as j,D as C,F as V,U as T,V as F,J as A,W as P,X as S,k as L,i as U,j as z,I as q,H as B}from"./index-12d8dd28.js";import{_ as R}from"./u-input.52919f31.js";import{_ as D,a as I}from"./u-form.b43211a9.js";import{_ as N}from"./u-icon.52b2e8b4.js";import{_ as O}from"./sms-code.vue_vue_type_script_setup_true_lang.bd6d03c4.js";import{_ as $}from"./u-checkbox.cf8c867e.js";import{_ as E}from"./u-checkbox-group.aeb040c3.js";import{_ as H}from"./uni-popup.cd7c8f59.js";import{t as J}from"./topTabbar.16728c6f.js";import{_ as Q}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-line.10a9c4d6.js";/* empty css                                                               */import"./u-modal.603e6547.js";import"./u-loading-icon.85553345.js";import"./u-popup.155f7cb9.js";import"./u-transition.5e23e77b.js";/* empty css                                                                     */import"./u-safe-bottom.a6f7ba25.js";const W=Q(e({__name:"login",setup(e){let Q={};J().setTopTabbarParam({title:"",topStatusBar:{bgColor:"#fff",textColor:"#333"}}),l((()=>Object.keys(Q).length?C(Number(Q.height))+C(Q.top)+C(8)+"rpx":"auto"));const W=a(!0),X=t(),Z=r(),G=a(""),K=a(!1),M=a(!1),Y=a(),ee=a(!0),le=()=>{ee.value=!ee.value},ae=()=>{Y.value.close()},te=()=>{K.value=!0,Y.value.close(),pe()};o((async e=>{await Z.getLoginConfig(),s()||Z.login.is_username||Z.login.is_mobile||(u({title:"商家未开启普通账号登录",icon:"none"}),setTimeout((()=>{i({url:"/app/pages/index/index",mode:"reLaunch"})}),100)),e.type?"mobile"==e.type?Z.login.is_mobile&&(G.value=e.type,uni.getStorageSync("pid")&&Object.assign(re,{pid:uni.getStorageSync("pid")})):"username"==e.type&&Z.login.is_username&&(G.value=e.type):Z.login.is_username?G.value="username":Z.login.is_mobile&&(G.value="mobile"),n()&&Z.login.is_auth_register?M.value=!0:M.value=!1}));const re=p({username:"",password:"",mobile:"",mobile_code:"",mobile_key:""});d((()=>{setTimeout((()=>{W.value=!1}),800)}));const oe=()=>{K.value=!K.value},se=()=>{G.value="username"==G.value?"mobile":"username"},ue=a(!1),ie=l((()=>({username:{type:"string",required:"username"==G.value,message:V("usernamePlaceholder"),trigger:["blur","change"]},password:{type:"string",required:"username"==G.value,message:V("passwordPlaceholder"),trigger:["blur","change"]},mobile:[{type:"string",required:"mobile"==G.value,message:V("mobilePlaceholder"),trigger:["blur","change"]},{validator:(e,l)=>"mobile"!=G.value||uni.$u.test.mobile(l),message:V("mobileError"),trigger:["change","blur"]}],mobile_code:{type:"string",required:"mobile"==G.value,message:V("codePlaceholder"),trigger:["blur","change"]}}))),ne=a(null),pe=()=>{ne.value.validate().then((()=>{if(Z.login.agreement_show&&!K.value)return Y.value.open(),!1;if(ue.value)return;ue.value=!0;("username"==G.value?T:F)(re).then((e=>{X.setToken(e.data.token),A().handleLoginBack()})).catch((()=>{ue.value=!1}))}))},de=()=>{const e=P();if(e.length>1){"app/pages/auth/index"==e[e.length-2].route?S({delta:1}):i({url:"/app/pages/auth/index",mode:"redirectTo"})}else i({url:"/app/pages/auth/index",mode:"redirectTo"})};return(e,l)=>{const a=L,t=U(z("u-input"),R),r=U(z("u-form-item"),D),o=U(z("u-icon"),N),s=U(z("sms-code"),O),u=U(z("u-form"),I),n=U(z("u-checkbox"),$),p=U(z("u-checkbox-group"),E),d=q,C=B,T=U(z("uni-popup"),H);return G.value?(m(),c(a,{key:0,class:"w-screen h-screen flex flex-col",style:j(e.themeColor())},{default:x((()=>[f(a,{class:"mx-[60rpx]"},{default:x((()=>[f(a,{class:"pt-[140rpx] text-[44rpx] font-500 text-[#333]"},{default:x((()=>[g(b("username"==G.value?_(V)("accountLogin"):_(V)("mobileLogin")),1)])),_:1}),f(a,{class:"text-[26rpx] leading-[39rpx] text-[var(--text-color-light6)] mt-[16rpx] mb-[80rpx]"},{default:x((()=>[g(b("username"==G.value?_(V)("accountLoginTip"):_(V)("mobileLoginTip")),1)])),_:1}),f(u,{labelPosition:"left",model:re,errorType:"toast",rules:_(ie),ref_key:"formRef",ref:ne},{default:x((()=>["username"==G.value?(m(),v(y,{key:0},[f(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6]"},{default:x((()=>[f(r,{label:"",prop:"username","border-bottom":!1},{default:x((()=>[f(t,{modelValue:re.username,"onUpdate:modelValue":l[0]||(l[0]=e=>re.username=e),border:"none",maxlength:"40",placeholder:_(V)("usernamePlaceholder"),autocomplete:"off",class:"!bg-transparent",disabled:W.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},null,8,["modelValue","placeholder","disabled"])])),_:1})])),_:1}),f(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx]"},{default:x((()=>[f(r,{label:"",prop:"password","border-bottom":!1},{default:x((()=>[f(t,{modelValue:re.password,"onUpdate:modelValue":l[1]||(l[1]=e=>re.password=e),border:"none",password:ee.value,maxlength:"40",placeholder:_(V)("passwordPlaceholder"),autocomplete:"new-password",class:"!bg-transparent",disabled:W.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},{suffix:x((()=>[re.password?(m(),c(a,{key:0,onClick:le},{default:x((()=>[f(o,{name:ee.value?"eye-off":"eye-fill",color:"#b9b9b9",size:"20"},null,8,["name"])])),_:1})):h("v-if",!0)])),_:1},8,["modelValue","password","placeholder","disabled"])])),_:1})])),_:1})],64)):h("v-if",!0),"mobile"==G.value?(m(),v(y,{key:1},[f(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6]"},{default:x((()=>[f(r,{label:"",prop:"mobile","border-bottom":!1},{default:x((()=>[f(t,{modelValue:re.mobile,"onUpdate:modelValue":l[2]||(l[2]=e=>re.mobile=e),type:"number",maxlength:"11",border:"none",placeholder:_(V)("mobilePlaceholder"),autocomplete:"off",class:"!bg-transparent",disabled:W.value,fontSize:"26rpx",placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},null,8,["modelValue","placeholder","disabled"])])),_:1})])),_:1}),f(a,{class:"h-[88rpx] flex w-full items-center px-[30rpx] rounded-[var(--goods-rounded-mid)] box-border bg-[#F6F6F6] mt-[40rpx] text-[26rpx]"},{default:x((()=>[f(r,{label:"",prop:"mobile_code","border-bottom":!1},{default:x((()=>[f(t,{modelValue:re.mobile_code,"onUpdate:modelValue":l[5]||(l[5]=e=>re.mobile_code=e),type:"number",maxlength:"4",border:"none",class:"!bg-transparent",fontSize:"26rpx",disabled:W.value,placeholder:_(V)("codePlaceholder"),placeholderClass:"!text-[var(--text-color-light9)] text-[26rpx]"},{suffix:x((()=>[_(Z).login.agreement_show?(m(),c(s,{key:0,mobile:re.mobile,type:"login",modelValue:re.mobile_key,"onUpdate:modelValue":l[3]||(l[3]=e=>re.mobile_key=e),isAgree:K.value},null,8,["mobile","modelValue","isAgree"])):(m(),c(s,{key:1,mobile:re.mobile,type:"login",modelValue:re.mobile_key,"onUpdate:modelValue":l[4]||(l[4]=e=>re.mobile_key=e)},null,8,["mobile","modelValue"]))])),_:1},8,["modelValue","disabled","placeholder"])])),_:1})])),_:1})],64)):h("v-if",!0)])),_:1},8,["model","rules"]),"username"==G.value?(m(),c(a,{key:0,class:"text-right text-[24rpx] text-[var(--text-color-light9)] leading-[34rpx] mt-[20rpx]",onClick:l[6]||(l[6]=e=>_(i)({url:"/app/pages/auth/resetpwd"}))},{default:x((()=>[g(b(_(V)("resetpwd")),1)])),_:1})):h("v-if",!0),f(a,{class:k({"mt-[160rpx]":"username"!=G.value,"mt-[106rpx]":"username"==G.value})},{default:x((()=>[_(Z).login.agreement_show?(m(),c(a,{key:0,class:"flex items-center mb-[20rpx] py-[14rpx]",onClick:w(oe,["stop"])},{default:x((()=>[f(p,{onChange:oe},{default:x((()=>[f(n,{activeColor:"var(--primary-color)",checked:K.value,shape:"circle",size:"30rpx"},null,8,["checked"])])),_:1}),f(a,{class:"text-[24rpx] text-[var(--text-color-light6)] flex items-center flex-wrap leading-[30rpx]"},{default:x((()=>[f(d,null,{default:x((()=>[g(b(_(V)("agreeTips")),1)])),_:1}),f(d,{onClick:l[7]||(l[7]=w((e=>_(i)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:x((()=>[g("《"+b(_(V)("privacyAgreement"))+"》",1)])),_:1}),f(d,null,{default:x((()=>[g(b(_(V)("and")),1)])),_:1}),f(d,{onClick:l[8]||(l[8]=w((e=>_(i)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:x((()=>[g("《"+b(_(V)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"])):h("v-if",!0),f(C,{class:"w-full h-[80rpx] !bg-[var(--primary-color)] text-[26rpx] rounded-[40rpx] leading-[80rpx] font-500 !text-[#fff] !mx-[0]",loadingText:_(V)("logining"),onClick:pe},{default:x((()=>[g(b(_(V)("login")),1)])),_:1},8,["loadingText"]),f(a,{class:"flex items-center justify-between mt-[30rpx]"},{default:x((()=>["username"==G.value&&_(Z).login.is_mobile||"mobile"==G.value&&_(Z).login.is_username?(m(),c(a,{key:0,class:"text-[26rpx] text-[var(--text-color-light6)] leading-[34rpx]",onClick:se},{default:x((()=>[g(b("username"==G.value?_(V)("mobileLogin"):_(V)("accountLogin")),1)])),_:1})):h("v-if",!0),f(a,{class:"text-[26rpx] text-[#333] leading-[34rpx]",onClick:l[9]||(l[9]=e=>_(i)({url:"/app/pages/auth/register",param:{type:G.value}}))},{default:x((()=>[f(d,null,{default:x((()=>[g(b(_(V)("noAccount"))+",",1)])),_:1}),f(d,{class:"text-primary"},{default:x((()=>[g(b(_(V)("toRegister")),1)])),_:1})])),_:1})])),_:1})])),_:1},8,["class"])])),_:1}),f(T,{ref_key:"popupRef",ref:Y,type:"dialog"},{default:x((()=>[f(a,{class:"bg-[#fff] flex flex-col justify-between w-[600rpx] min-h-[280rpx] rounded-[var(--rounded-big)] box-border px-[35rpx] pt-[35rpx] pb-[8rpx] relative"},{default:x((()=>[f(a,{class:"flex justify-center"},{default:x((()=>[f(d,{class:"text-[33rpx] font-700"},{default:x((()=>[g(" 用户协议及隐私保护")])),_:1})])),_:1}),f(a,{class:"flex items-center mb-[20rpx] mt-[20rpx] py-[20rpx]",onClick:w(oe,["stop"])},{default:x((()=>[f(a,{class:"text-[26rpx] text-[var(--text-color-light6)] flex items-center flex-wrap"},{default:x((()=>[f(d,null,{default:x((()=>[g(b(_(V)("agreeTips")),1)])),_:1}),f(d,{onClick:l[10]||(l[10]=w((e=>_(i)({url:"/app/pages/auth/agreement?key=privacy"})),["stop"])),class:"text-primary"},{default:x((()=>[g("《"+b(_(V)("privacyAgreement"))+"》",1)])),_:1}),f(d,null,{default:x((()=>[g(b(_(V)("and")),1)])),_:1}),f(d,{onClick:l[11]||(l[11]=w((e=>_(i)({url:"/app/pages/auth/agreement?key=service"})),["stop"])),class:"text-primary"},{default:x((()=>[g("《"+b(_(V)("userAgreement"))+"》",1)])),_:1})])),_:1})])),_:1},8,["onClick"]),f(a,null,{default:x((()=>[f(a,{class:"w-[100%] flex justify-center bg-[var(--primary-color)] h-[70rpx] leading-[70rpx] text-[#fff] text-[26rpx] border-[0] font-500 rounded-[50rpx]",onClick:te},{default:x((()=>[g("同意并登录")])),_:1}),f(a,{class:"w-[100%] flex justify-center h-[70rpx] leading-[70rpx] text-[#999] text-[24rpx] border-[0] font-500 rounded-[50rpx]",onClick:ae},{default:x((()=>[g("不同意")])),_:1})])),_:1})])),_:1})])),_:1},512),M.value?(m(),c(a,{key:0,class:"footer w-full"},{default:x((()=>[f(a,{class:"text-[26rpx] leading-[36rpx] text-[#333] text-center mb-[30rpx] font-400"},{default:x((()=>[g(b(_(V)("oneClicklogin")),1)])),_:1}),f(a,{class:"flex justify-center"},{default:x((()=>[f(C,{class:"h-[80rpx] w-[80rpx] text-[46rpx] !text-[#1AAB37] text-center !p-0 !bg-transparent leading-[79rpx] border-[2rpx] rounded-[50%] border-solid border-[#ddd] nc-iconfont nc-icon-weixinV6mm overflow-hidden",onClick:de})])),_:1})])),_:1})):h("v-if",!0)])),_:1},8,["style"])):h("v-if",!0)}}}),[["__scopeId","data-v-92b2da28"]]);export{W as default};
