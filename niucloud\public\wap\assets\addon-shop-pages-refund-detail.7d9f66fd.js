import{a5 as e,a6 as l,a0 as t,o as a,c as r,w as s,$ as o,d,r as u,l as i,s as n,M as p,E as c,b as f,A as x,B as m,e as _,g,C as v,n as b,a as h,k as y,G as w,i as j,j as k,I as F,H as S,F as C}from"./index-12d8dd28.js";import{_ as I}from"./u--image.376d9df2.js";import{p as V,_ as T}from"./u-input.52919f31.js";import{_ as B}from"./_plugin-vue_export-helper.1b428a4d.js";import{p as P,a as $,_ as O}from"./u-form.b43211a9.js";import{_ as A}from"./u-tabbar.82e98c18.js";import{_ as z}from"./u-modal.603e6547.js";import{_ as E}from"./loading-page.vue_vue_type_script_setup_true_lang.1c76425e.js";import{t as W}from"./topTabbar.16728c6f.js";import{c as L,i as D,r as R,h as q}from"./refund.8cce8a85.js";import{l as H}from"./logistics-tracking.e8c7b74e.js";import"./u-image.f86d3186.js";import"./u-icon.52b2e8b4.js";/* empty css                                                               */import"./u-transition.5e23e77b.js";/* empty css                                                                     *//* empty css                                                                */import"./u-line.10a9c4d6.js";import"./u-safe-bottom.a6f7ba25.js";import"./u-loading-icon.85553345.js";import"./u-popup.155f7cb9.js";import"./u-steps.f5e49b28.js";import"./u-text.40aa3da3.js";import"./order.5fba8f66.js";const U=B({name:"u--input",mixins:[e,V,l],components:{uvInput:T}},[["render",function(e,l,d,u,i,n){const p=t("uvInput");return a(),r(p,{modelValue:e.modelValue,"onUpdate:modelValue":l[0]||(l[0]=l=>e.$emit("update:modelValue",l)),type:e.type,fixed:e.fixed,disabled:e.disabled,disabledColor:e.disabledColor,clearable:e.clearable,password:e.password,maxlength:e.maxlength,placeholder:e.placeholder,placeholderClass:e.placeholderClass,placeholderStyle:e.placeholderStyle,showWordLimit:e.showWordLimit,confirmType:e.confirmType,confirmHold:e.confirmHold,holdKeyboard:e.holdKeyboard,focus:e.focus,autoBlur:e.autoBlur,disableDefaultPadding:e.disableDefaultPadding,cursor:e.cursor,cursorSpacing:e.cursorSpacing,selectionStart:e.selectionStart,selectionEnd:e.selectionEnd,adjustPosition:e.adjustPosition,inputAlign:e.inputAlign,fontSize:e.fontSize,color:e.color,prefixIcon:e.prefixIcon,suffixIcon:e.suffixIcon,suffixIconStyle:e.suffixIconStyle,prefixIconStyle:e.prefixIconStyle,border:e.border,readonly:e.readonly,shape:e.shape,customStyle:e.customStyle,formatter:e.formatter,ignoreCompositionEvent:e.ignoreCompositionEvent},{default:s((()=>[o(e.$slots,"prefix",{slot:"prefix"}),o(e.$slots,"suffix",{slot:"suffix"})])),_:3},8,["modelValue","type","fixed","disabled","disabledColor","clearable","password","maxlength","placeholder","placeholderClass","placeholderStyle","showWordLimit","confirmType","confirmHold","holdKeyboard","focus","autoBlur","disableDefaultPadding","cursor","cursorSpacing","selectionStart","selectionEnd","adjustPosition","inputAlign","fontSize","color","prefixIcon","suffixIcon","suffixIconStyle","prefixIconStyle","border","readonly","shape","customStyle","formatter","ignoreCompositionEvent"])}]]);const K=B({name:"u--form",mixins:[e,P,l],components:{uvForm:$},created(){this.children=[]},methods:{setRules(e){this.$refs.uForm.setRules(e)},validate(){return this.$refs.uForm.validate()},validateField(e,l){return this.$refs.uForm.validateField(e,l)},resetFields(){return this.$refs.uForm.resetFields()},clearValidate(e){return this.$refs.uForm.clearValidate(e)},setMpData(){this.$refs.uForm.children=this.children}}},[["render",function(e,l,d,u,i,n){const p=t("uvForm");return a(),r(p,{ref:"uForm",model:e.model,rules:e.rules,errorType:e.errorType,borderBottom:e.borderBottom,labelPosition:e.labelPosition,labelWidth:e.labelWidth,labelAlign:e.labelAlign,labelStyle:e.labelStyle,customStyle:e.customStyle},{default:s((()=>[o(e.$slots,"default")])),_:3},8,["model","rules","errorType","borderBottom","labelPosition","labelWidth","labelAlign","labelStyle","customStyle"])}]]),M=B(d({__name:"detail",setup(e){W().setTopTabbarParam({title:"退款详情"});const l=u({}),t=u(!0),o=u(""),d=u(""),V=u(!1),T=u("");u("");const B=u(""),P=u({express_number:"",express_company:"",remark:""}),$=i((()=>({express_number:{type:"string",required:!0,message:"请输入物流单号",trigger:["blur","change"]},express_company:{type:"string",required:!0,message:"请输入物流公司",trigger:["blur","change"]}})));u(null),n((e=>{if(o.value=e.order_refund_no,o.value)d.value=e.type,V.value=e.is_edit_delivery,M(o.value);else{p({url:"/addon/shop/pages/refund/list",title:"缺少订单号"})}}));const M=e=>{t.value=!0,L(e).then((e=>{l.value=e.data,V.value&&l.value.delivery&&(P.value.express_number=l.value.delivery.express_number,P.value.express_company=l.value.delivery.express_company,P.value.remark=l.value.delivery.remark),T.value=l.value.order_goods.goods_name,B.value=c(l.value.order_goods.goods_image_thumb_small||""),t.value=!1})).catch((()=>{t.value=!1}))},G=e=>{h({url:"/addon/shop/pages/goods/detail",param:{goods_id:e}})},J=u(),N=()=>{J.value.validate().then((e=>{let t={delivery:P.value,order_refund_no:l.value.order_refund_no};(V.value?D(t):R(t)).then((e=>{setTimeout((()=>{h({url:"/addon/shop/pages/refund/list"})}),500)})).catch((()=>{}))}))},Q=e=>{"cancel"==e?(X=l.value.order_refund_no,Z.value=!0):"edit"==e?h({url:"/addon/shop/pages/refund/edit_apply",param:{order_refund_no:l.value.order_refund_no}}):"logistics"==e?h({url:"/addon/shop/pages/refund/detail",param:{order_refund_no:l.value.order_refund_no,type:"logistics"}}):"editLogistics"==e&&h({url:"/addon/shop/pages/refund/detail",param:{order_refund_no:l.value.order_refund_no,type:"logistics",is_edit_delivery:!0}})},Z=u(!1);let X="";const Y=()=>{q(X).then((e=>{Z.value=!1,M(o.value)})).catch((()=>{Z.value=!1}))},ee=()=>{Z.value=!1};return(e,u)=>{const i=y,n=w,p=j(k("u--image"),I),V=F,T=j(k("u--input"),U),B=j(k("u-form-item"),O),W=j(k("u--form"),K),L=S,D=j(k("u-tabbar"),A),R=j(k("u-modal"),z),q=j(k("loading-page"),E);return a(),r(i,{style:b(e.themeColor())},{default:s((()=>[t.value?g("v-if",!0):(a(),r(i,{key:0,class:"bg-[var(--page-bg-color)] min-h-screen overflow-hidden"},{default:s((()=>["logistics"!=d.value?(a(),r(i,{key:0,class:"pb-[200rpx]"},{default:s((()=>[f(i,{class:"bg-linear"},{default:s((()=>[l.value.status_name?(a(),r(i,{key:0,class:"flex justify-between items-center pl-[40rpx] pr-[50rpx] h-[280rpx] box-border pb-[90rpx]"},{default:s((()=>[f(i,{class:"text-[36rpx] font-500 leading-[42rpx] text-[#fff]"},{default:s((()=>[x(m(l.value.status_name),1)])),_:1}),f(i,{class:"flex items-center"},{default:s((()=>[-1!=["1","2","4","6","7"].indexOf(l.value.status)?(a(),r(n,{key:0,class:"w-[180rpx] h-[140rpx]",src:_(c)("addon/shop/detail/payment.png"),mode:"aspectFit"},null,8,["src"])):g("v-if",!0),-1!=["8"].indexOf(l.value.status)?(a(),r(n,{key:1,class:"w-[180rpx] h-[140rpx]",src:_(c)("addon/shop/detail/complete.png"),mode:"aspectFit"},null,8,["src"])):g("v-if",!0),-1!=["3","5","-1"].indexOf(l.value.status)?(a(),r(n,{key:2,class:"w-[180rpx] h-[140rpx]",src:_(c)("addon/shop/detail/close.png"),mode:"aspectFit"},null,8,["src"])):g("v-if",!0)])),_:1})])),_:1})):g("v-if",!0)])),_:1}),f(i,{class:"sidebar-margin card-template flex justify-between flex-wrap mt-[-76rpx]"},{default:s((()=>[f(i,{class:"w-[150rpx] h-[150rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",onClick:u[0]||(u[0]=e=>G(l.value.order_goods.goods_id))},{default:s((()=>[f(p,{radius:"var(--goods-rounded-big)",width:"150rpx",height:"150rpx",src:_(c)(l.value.order_goods.goods_image_thumb_small?l.value.order_goods.goods_image_thumb_small:""),model:"aspectFill"},{error:s((()=>[f(n,{class:"w-[150rpx] h-[150rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:_(c)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:1},8,["src"])])),_:1}),f(i,{class:"ml-[20rpx] flex flex-1 flex-col justify-between"},{default:s((()=>[f(i,null,{default:s((()=>[f(i,{class:"text-[28rpx] max-w-[490rpx] truncate leading-[40rpx]"},{default:s((()=>[x(m(l.value.order_goods.goods_name),1)])),_:1}),l.value.order_goods.sku_name?(a(),r(i,{key:0,class:"text-[22rpx] mt-[10rpx] text-[var(--text-color-light9)] truncate max-w-[490rpx] leading-[28rpx]"},{default:s((()=>[x(m(l.value.order_goods.sku_name),1)])),_:1})):g("v-if",!0)])),_:1}),f(i,{class:"flex justify-between items-center leading-[28rpx]"},{default:s((()=>[f(i,{class:"price-font"},{default:s((()=>[f(V,{class:"text-[24rpx]"},{default:s((()=>[x("￥")])),_:1}),f(V,{class:"text-[40rpx] font-500"},{default:s((()=>[x(m(parseFloat(l.value.order_goods.price).toFixed(2).split(".")[0]),1)])),_:1}),f(V,{class:"text-[24rpx] font-500"},{default:s((()=>[x("."+m(parseFloat(l.value.order_goods.price).toFixed(2).split(".")[1]),1)])),_:1})])),_:1}),f(V,{class:"text-right text-[26rpx]"},{default:s((()=>[x("x"+m(l.value.order_goods.num),1)])),_:1})])),_:1})])),_:1})])),_:1}),f(i,{class:"sidebar-margin mt-[var(--top-m)] card-template"},{default:s((()=>[f(i,{class:"justify-between text-[28rpx] card-template-item"},{default:s((()=>[f(i,null,{default:s((()=>[x(m(_(C)("refundMoney")),1)])),_:1}),f(i,{class:"price-font text-[var(--price-text-color)]"},{default:s((()=>[f(V,{class:"text-[24rpx] mr-[4rpx]"},{default:s((()=>[x("￥")])),_:1}),8==l.value.status?(a(),r(V,{key:0,class:"text-[28rpx]"},{default:s((()=>[x(m(parseFloat(l.value.money).toFixed(2)),1)])),_:1})):(a(),r(V,{key:1,class:"text-[28rpx]"},{default:s((()=>[x(m(parseFloat(l.value.apply_money).toFixed(2)),1)])),_:1}))])),_:1})])),_:1}),f(i,{class:"justify-between text-[28rpx] card-template-item"},{default:s((()=>[f(i,null,{default:s((()=>[x(m(_(C)("refundType")),1)])),_:1}),f(i,null,{default:s((()=>[x(m(l.value.refund_type_name),1)])),_:1})])),_:1}),f(i,{class:"justify-between text-[28rpx] card-template-item"},{default:s((()=>[f(i,null,{default:s((()=>[x(m(_(C)("refundCause")),1)])),_:1}),f(i,{class:"w-[400rpx] multi-hidden text-right"},{default:s((()=>[x(m(l.value.reason||"--"),1)])),_:1})])),_:1}),f(i,{class:"justify-between text-[28rpx] card-template-item"},{default:s((()=>[f(i,null,{default:s((()=>[x(m(_(C)("refundNo")),1)])),_:1}),f(i,null,{default:s((()=>[x(m(l.value.order_refund_no),1)])),_:1})])),_:1}),f(i,{class:"justify-between text-[28rpx] card-template-item"},{default:s((()=>[f(i,null,{default:s((()=>[x(m(_(C)("createTime")),1)])),_:1}),f(i,null,{default:s((()=>[x(m(l.value.create_time),1)])),_:1})])),_:1}),f(i,{class:"justify-between text-[28rpx] card-template-item !items-baseline"},{default:s((()=>[f(i,null,{default:s((()=>[x(m(_(C)("createExplain")),1)])),_:1}),f(i,{class:"flex-1 ml-[60rpx] text-right leading-[1.5] flex justify-end break-all"},{default:s((()=>[x(m(l.value.remark),1)])),_:1})])),_:1}),l.value.shop_reason?(a(),r(i,{key:0,class:"justify-between text-[28rpx] card-template-item !items-baseline break-all"},{default:s((()=>[f(i,null,{default:s((()=>[x(m(_(C)("reasonRefusal")),1)])),_:1}),f(i,{class:"flex-1 ml-[60rpx] leading-[1.5] text-right text-[#333]"},{default:s((()=>[x(m(l.value.shop_reason),1)])),_:1})])),_:1})):g("v-if",!0)])),_:1}),f(i,{class:"sidebar-margin mt-[var(--top-m)] card-template"},{default:s((()=>[f(i,{class:"justify-between text-[28rpx] card-template-item"},{default:s((()=>[f(i,null,{default:s((()=>[x(m(_(C)("record")),1)])),_:1}),f(i,{class:"flex items-center",onClick:u[1]||(u[1]=e=>_(h)({url:"/addon/shop/pages/refund/log",param:{order_refund_no:o.value}}))},{default:s((()=>[f(V,{class:"text-[26rpx] text-[var(--text-color-light9)]"},{default:s((()=>[x(m(_(C)("check")),1)])),_:1}),f(V,{class:"nc-iconfont nc-icon-youV6xx text-[24rpx] text-[var(--text-color-light9)] pt-[2rpx]"})])),_:1})])),_:1})])),_:1}),f(i,{class:"flex tab-bar justify-between items-center bg-[#fff] fixed left-0 right-0 bottom-0 min-h-[100rpx] pl-[30rpx] pr-[20rpx] flex-wrap"},{default:s((()=>[f(i,{class:"flex"},{default:s((()=>[f(i,{class:"flex mr-[20rpx] flex-col justify-center items-center",onClick:u[2]||(u[2]=e=>_(h)({url:"/addon/shop/pages/index",mode:"reLaunch"}))},{default:s((()=>[f(i,{class:"nc-iconfont nc-icon-shouyeV6xx11 text-[36rpx]"}),f(V,{class:"text-[20rpx] mt-[10rpx]"},{default:s((()=>[x(m(_(C)("index")),1)])),_:1})])),_:1})])),_:1}),f(i,{class:"flex justify-end"},{default:s((()=>[-1==["6","7","8","-1","-3"].indexOf(l.value.status)?(a(),r(i,{key:0,class:"min-w-[180rpx] box-border text-[26rpx] h-[70rpx] flex-center border-[2rpx] border-solid border-[#ccc] text-[#333] rounded-full ml-[20rpx]",onClick:u[3]||(u[3]=e=>Q("cancel"))},{default:s((()=>[x(m(_(C)("refundApply")),1)])),_:1})):g("v-if",!0),-1!=["3"].indexOf(l.value.status)?(a(),r(i,{key:1,class:"min-w-[180rpx] box-border text-[#333] text-[26rpx] h-[70rpx] flex-center border-[2rpx] border-solid border-[#ccc] rounded-full ml-[20rpx] px-[20rpx]",onClick:u[4]||(u[4]=v((e=>Q("edit")),["stop"]))},{default:s((()=>[x("编辑退款信息")])),_:1})):g("v-if",!0),-1!=["2"].indexOf(l.value.status)?(a(),r(i,{key:2,class:"min-w-[180rpx] box-border text-[#333] text-[26rpx] h-[70rpx] flex-center border-[2rpx] border-solid border-[#ccc] rounded-full ml-[20rpx] px-[20rpx]",onClick:u[5]||(u[5]=v((e=>Q("logistics")),["stop"]))},{default:s((()=>[x("填写发货物流")])),_:1})):g("v-if",!0),-1!=["5"].indexOf(l.value.status)?(a(),r(i,{key:3,class:"min-w-[180rpx] box-border text-[#333] text-[26rpx] h-[70rpx] flex-center border-[2rpx] border-solid border-[#ccc] rounded-full ml-[20rpx] px-[20rpx]",onClick:u[6]||(u[6]=v((e=>Q("editLogistics")),["stop"]))},{default:s((()=>[x("编辑发货物流")])),_:1})):g("v-if",!0)])),_:1})])),_:1})])),_:1})):(a(),r(i,{key:1},{default:s((()=>[f(i,{class:"bg-linear"},{default:s((()=>[f(i,{class:"flex justify-between items-center pl-[40rpx] pr-[50rpx] h-[280rpx] box-border pb-[90rpx]"},{default:s((()=>[f(i,{class:"text-[36rpx] font-500 leading-[42rpx] text-[#fff]"},{default:s((()=>[x(m(l.value.status_name),1)])),_:1}),f(i,{class:"flex items-center"},{default:s((()=>[f(n,{class:"w-[180rpx] h-[140rpx]",src:_(c)("addon/shop/detail/payment.png"),mode:"aspectFit"},null,8,["src"])])),_:1})])),_:1})])),_:1}),f(i,{class:"sidebar-margin card-template mt-[-79rpx] flex justify-between flex-wrap"},{default:s((()=>[f(i,{class:"w-[150rpx] h-[150rpx] flex-2",onClick:u[7]||(u[7]=e=>G(l.value.order_goods.goods_id))},{default:s((()=>[f(p,{radius:"var(--goods-rounded-big)",width:"150rpx",height:"150rpx",src:_(c)(l.value.order_goods.sku_image?l.value.order_goods.sku_image.split(",")[0]:""),model:"aspectFill"},{error:s((()=>[f(n,{class:"w-[150rpx] h-[150rpx] rounded-[var(--goods-rounded-big)] overflow-hidden",src:_(c)("static/resource/images/diy/shop_default.jpg"),mode:"aspectFill"},null,8,["src"])])),_:1},8,["src"])])),_:1}),f(i,{class:"ml-[20rpx] flex flex-1 flex-col justify-between"},{default:s((()=>[f(i,null,{default:s((()=>[f(i,{class:"text-[28rpx] max-w-[490rpx] truncate leading-[40rpx]"},{default:s((()=>[x(m(l.value.order_goods.goods_name),1)])),_:1}),l.value.order_goods.sku_name?(a(),r(i,{key:0,class:"text-[24rpx] mt-[14rpx] text-[var(--text-color-light9)] truncate max-w-[490rpx] leading-[28rpx]"},{default:s((()=>[x(m(l.value.order_goods.sku_name),1)])),_:1})):g("v-if",!0)])),_:1}),f(i,{class:"flex justify-between items-center leading-[28rpx]"},{default:s((()=>[f(i,{class:"price-font"},{default:s((()=>[f(V,{class:"text-[24rpx] font-500"},{default:s((()=>[x("￥")])),_:1}),f(V,{class:"text-[40rpx] font-500"},{default:s((()=>[x(m(parseFloat(l.value.order_goods.price).toFixed(2).split(".")[0]),1)])),_:1}),f(V,{class:"text-[24rpx] font-500"},{default:s((()=>[x("."+m(parseFloat(l.value.order_goods.price).toFixed(2).split(".")[1]),1)])),_:1})])),_:1}),f(V,{class:"text-right text-[26rpx]"},{default:s((()=>[x("x"+m(l.value.order_goods.num),1)])),_:1})])),_:1})])),_:1})])),_:1}),f(i,{class:"sidebar-margin card-template top-mar"},{default:s((()=>[f(i,{class:"card-template-item justify-between text-[28rpx]"},{default:s((()=>[f(i,null,{default:s((()=>[x("联系人")])),_:1}),f(i,null,{default:s((()=>[x(m(l.value.refund_address.contact_name),1)])),_:1})])),_:1}),f(i,{class:"card-template-item justify-between text-[28rpx]"},{default:s((()=>[f(i,null,{default:s((()=>[x("手机号")])),_:1}),f(i,null,{default:s((()=>[x(m(l.value.refund_address.mobile),1)])),_:1})])),_:1}),f(i,{class:"card-template-item justify-between text-[28rpx]"},{default:s((()=>[f(i,null,{default:s((()=>[x("退货地址")])),_:1}),l.value.refund_address?(a(),r(i,{key:0,class:"w-[460rpx] text-sm text-right"},{default:s((()=>[x(m(l.value.refund_address.full_address||"--"),1)])),_:1})):g("v-if",!0)])),_:1})])),_:1}),f(i,{class:"sidebar-margin card-template top-mar py-[var(--top-m)] mb-[var(--top-m)]"},{default:s((()=>[f(i,{class:"title"},{default:s((()=>[x("物流信息")])),_:1}),f(W,{labelPosition:"left",model:P.value,rules:_($),errorType:"toast",ref_key:"deliveryForm",ref:J,labelWidth:"140rpx",labelStyle:{fontSize:"28rpx"}},{default:s((()=>[f(B,{label:"物流公司",prop:"express_company",borderBottom:!1},{default:s((()=>[f(T,{border:"none",modelValue:P.value.express_company,"onUpdate:modelValue":u[8]||(u[8]=e=>P.value.express_company=e),placeholder:"请输入物流公司",placeholderClass:"text-sm !text-[var(--text-color-light9)]",fontSize:"28rpx",maxlength:"50"},null,8,["modelValue"])])),_:1}),f(i,{class:"mt-[16rpx]"},{default:s((()=>[f(B,{label:"物流单号",prop:"express_number",borderBottom:!1},{default:s((()=>[f(T,{border:"none",placeholder:"请输入物流单号",modelValue:P.value.express_number,"onUpdate:modelValue":u[9]||(u[9]=e=>P.value.express_number=e),placeholderClass:"text-sm !text-[var(--text-color-light9)]",fontSize:"28rpx",maxlength:"100"},null,8,["modelValue"])])),_:1})])),_:1}),f(i,{class:"mt-[16rpx]"},{default:s((()=>[f(B,{label:"物流说明",borderBottom:!1},{default:s((()=>[f(T,{border:"none",placeholder:"选填",modelValue:P.value.remark,"onUpdate:modelValue":u[10]||(u[10]=e=>P.value.remark=e),placeholderClass:"text-sm !text-[var(--text-color-light9)]",fontSize:"28rpx",maxlength:"100"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1}),f(D,{fixed:!0,placeholder:!0,safeAreaInsetBottom:!0,zIndex:"10"},{default:s((()=>[f(i,{class:"flex-1 pl-[var(--sidebar-m)] pr-[var(--sidebar-m)] bg-[var(--page-bg-color)]"},{default:s((()=>[f(L,{class:"primary-btn-bg text-[#fff] h-[80rpx] leading-[80rpx] rounded-[100rpx] text-[26rpx] font-500","hover-class":"none",onClick:N},{default:s((()=>[x("提交")])),_:1})])),_:1})])),_:1})])),_:1})),f(H,{ref:"materialRef"},null,512),f(R,{show:Z.value,confirmColor:"var(--primary-color)",content:_(C)("cancelRefundContent"),showCancelButton:!0,closeOnClickOverlay:!0,onCancel:ee,onConfirm:Y},null,8,["show","content"])])),_:1})),f(q,{loading:t.value},null,8,["loading"])])),_:1},8,["style"])}}}),[["__scopeId","data-v-33e62ca9"]]);export{M as default};
