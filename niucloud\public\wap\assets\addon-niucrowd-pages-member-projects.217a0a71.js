import{r as a,Q as t,y as e,o as s,c as l,w as c,k as u,g as o,b as r,R as n,a3 as i,S as d,A as p,b8 as _,I as f,H as v,T as m,B as g,n as j,C as b,G as y}from"./index-12d8dd28.js";import{l as k}from"./project.82b2754b.js";import{_ as w}from"./_plugin-vue_export-helper.1b428a4d.js";const h=w({__name:"projects",setup(w){const h=[{label:"全部",value:""},{label:"审核中",value:0},{label:"进行中",value:1},{label:"已成功",value:2},{label:"已失败",value:3}],C=a(""),x=a(!1),$=a([]),A=a(!0),F=a(1),I=a("/static/resource/images/diy/block_placeholder.png");t((()=>{M()}));const M=async(a=!1)=>{if(!x.value){x.value=!0;try{a||(F.value=1,$.value=[]);const t=await k({status:C.value,page:F.value,limit:10});if(t&&t.data){const e=Array.isArray(t.data)?t.data:t.data.data||[],s=e.map((a=>({...a,description:a.project_desc||a.description||"",cover_image:a.cover_image||"",target_amount:parseFloat(a.target_amount)||0,current_amount:parseFloat(a.current_amount)||0,support_count:parseInt(a.support_count)||0})));a?$.value.push(...s):$.value=s,A.value=e.length>=10}else a||($.value=[]),A.value=!1}catch(t){console.error("加载项目列表失败:",t),e({title:"加载失败",icon:"none"}),a||($.value=[]),A.value=!1}finally{x.value=!1}}},W=()=>{F.value++,M(!0)},B=a=>["审核中","进行中","已成功","已失败"][a]||"未知",E=()=>{_({url:"/addon/niucrowd/pages/project/create"})},G=a=>{console.warn("图片加载失败:",a)};return(a,t)=>{const k=u,w=f,F=v,H=y;return s(),l(k,{class:"my-projects-page"},{default:c((()=>[o(" 状态筛选 "),r(k,{class:"filter-tabs"},{default:c((()=>[(s(),n(d,null,i(h,((a,t)=>r(k,{key:t,class:m(["tab-item",{active:C.value===a.value}]),onClick:t=>{return e=a.value,C.value=e,void M();var e}},{default:c((()=>[p(g(a.label),1)])),_:2},1032,["class","onClick"]))),64))])),_:1}),o(" 项目列表 "),r(k,{class:"project-list"},{default:c((()=>[x.value?(s(),l(k,{key:0,class:"loading-wrapper"},{default:c((()=>[r(w,{class:"loading-text"},{default:c((()=>[p("加载中...")])),_:1})])),_:1})):0===$.value.length?(s(),l(k,{key:1,class:"empty-wrapper"},{default:c((()=>[r(k,{class:"empty-icon"},{default:c((()=>[r(w,{class:"iconfont iconwushuju"})])),_:1}),r(w,{class:"empty-text"},{default:c((()=>[p("暂无项目")])),_:1}),r(F,{class:"create-btn",onClick:E},{default:c((()=>[p("发起项目")])),_:1})])),_:1})):(s(),l(k,{key:2},{default:c((()=>[(s(!0),n(d,null,i($.value,(a=>(s(),l(k,{key:a.project_id,class:"project-item",onClick:t=>{return e=a.project_id,void _({url:`/addon/niucrowd/pages/project/detail?id=${e}`});var e}},{default:c((()=>[r(k,{class:"project-image"},{default:c((()=>{return[r(H,{src:(e=a.cover_image,e?e.startsWith("blob:")?I.value:e.startsWith("/")?window.location.origin+e:e:I.value),mode:"aspectFill",class:"cover-img",onError:G},null,8,["src"]),r(k,{class:m(["status-badge",(t=a.status,["pending","active","success","failed"][t]||"pending")])},{default:c((()=>[p(g(B(a.status)),1)])),_:2},1032,["class"])];var t,e})),_:2},1024),r(k,{class:"project-info"},{default:c((()=>[r(k,{class:"project-name"},{default:c((()=>[p(g(a.project_name),1)])),_:2},1024),r(k,{class:"project-desc"},{default:c((()=>[p(g(a.project_desc||a.description||""),1)])),_:2},1024),r(k,{class:"project-stats"},{default:c((()=>[r(k,{class:"stat-row"},{default:c((()=>[r(w,{class:"stat-label"},{default:c((()=>[p("目标金额：")])),_:1}),r(w,{class:"stat-value"},{default:c((()=>[p("¥"+g(a.target_amount),1)])),_:2},1024)])),_:2},1024),r(k,{class:"stat-row"},{default:c((()=>[r(w,{class:"stat-label"},{default:c((()=>[p("已筹金额：")])),_:1}),r(w,{class:"stat-value primary"},{default:c((()=>[p("¥"+g(a.current_amount),1)])),_:2},1024)])),_:2},1024),r(k,{class:"stat-row"},{default:c((()=>[r(w,{class:"stat-label"},{default:c((()=>[p("支持人数：")])),_:1}),r(w,{class:"stat-value"},{default:c((()=>[p(g(a.support_count)+"人",1)])),_:2},1024)])),_:2},1024)])),_:2},1024),r(k,{class:"progress-info"},{default:c((()=>[r(k,{class:"progress-bar"},{default:c((()=>[r(k,{class:"progress-fill",style:j({width:Math.min(a.current_amount/a.target_amount*100,100)+"%"})},null,8,["style"])])),_:2},1024),r(w,{class:"progress-text"},{default:c((()=>[p(g(Math.round(a.current_amount/a.target_amount*100))+"%",1)])),_:2},1024)])),_:2},1024),r(k,{class:"project-actions"},{default:c((()=>[o(" 只有待审核(0)和审核失败(3)的项目可以编辑 "),0===a.status||3===a.status?(s(),l(F,{key:0,class:"action-btn",onClick:b((t=>(a=>{_({url:`/addon/niucrowd/pages/project/edit?id=${a.project_id}`})})(a)),["stop"])},{default:c((()=>[p("编辑")])),_:2},1032,["onClick"])):o("v-if",!0),o(" 只有进行中(1)的项目可以发布动态 "),1===a.status?(s(),l(F,{key:1,class:"action-btn update-btn",onClick:b((t=>(a=>{1===a.status?_({url:`/addon/niucrowd/pages/member/update-publish?project_id=${a.project_id}`}):e({title:"只有进行中的项目才能发布动态",icon:"none"})})(a)),["stop"])},{default:c((()=>[r(w,{class:"iconfont icondongtai"}),p(" 发布动态 ")])),_:2},1032,["onClick"])):o("v-if",!0),o(" 进行中(1)、成功(2)、失败(3)、已取消(4)的项目显示状态标签 "),1===a.status?(s(),l(k,{key:2,class:"status-tag active"},{default:c((()=>[p("进行中")])),_:1})):o("v-if",!0),2===a.status?(s(),l(k,{key:3,class:"status-tag success"},{default:c((()=>[p("已成功")])),_:1})):o("v-if",!0),4===a.status?(s(),l(k,{key:4,class:"status-tag cancelled"},{default:c((()=>[p("已取消")])),_:1})):o("v-if",!0),r(F,{class:"action-btn primary",onClick:b((t=>(a=>{_({url:`/addon/niucrowd/pages/project/data?id=${a.project_id}`})})(a)),["stop"])},{default:c((()=>[p("数据")])),_:2},1032,["onClick"])])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1}))])),_:1}),o(" 加载更多 "),A.value&&!x.value?(s(),l(k,{key:0,class:"load-more",onClick:W},{default:c((()=>[r(w,null,{default:c((()=>[p("加载更多")])),_:1})])),_:1})):o("v-if",!0)])),_:1})}}},[["__scopeId","data-v-5287b075"]]);export{h as default};
