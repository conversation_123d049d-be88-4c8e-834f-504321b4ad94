import{r as a,l as e,Q as l,y as s,o as t,c as u,w as o,k as r,g as c,b as n,R as d,a3 as i,S as p,A as v,B as f,e as _,b9 as g,I as m,H as y,T as b,b8 as j,G as w}from"./index-12d8dd28.js";import{m as h}from"./project.82b2754b.js";import{_ as k}from"./_plugin-vue_export-helper.1b428a4d.js";const x=k({__name:"supports",setup(k){const x=[{label:"全部",value:""},{label:"待支付",value:0},{label:"已支付",value:1},{label:"已退款",value:2}],A=a(""),C=a(!1),M=a([]),F=a(!0),I=a(1),S=a("/static/resource/images/diy/block_placeholder.png"),D=e((()=>M.value.length)),P=e((()=>M.value.reduce(((a,e)=>a+parseFloat(e.amount||0)),0).toFixed(2))),L=e((()=>M.value.filter((a=>{var e;return 2===(null==(e=a.project)?void 0:e.status)})).length));l((()=>{B()}));const B=async(a=!1)=>{if(!C.value){C.value=!0;try{a||(I.value=1,M.value=[]),console.log("🔍 调用getMySupports API，参数:",{pay_status:A.value,page:I.value,limit:10});const e=await h({pay_status:A.value,page:I.value,limit:10});if(console.log("📡 getMySupports API响应:",e),e&&e.data){const l=Array.isArray(e.data)?e.data:e.data.data||[];console.log("📋 处理后的支持列表数据:",l),console.log("📊 支持列表长度:",l.length),a?M.value.push(...l):M.value=l,console.log("✅ 最终supportList:",M.value),console.log("📈 统计数据计算:"),console.log("  - totalSupports:",M.value.length),console.log("  - totalAmount:",M.value.reduce(((a,e)=>a+parseFloat(e.amount||0)),0).toFixed(2)),console.log("  - successCount:",M.value.filter((a=>{var e;return 2===(null==(e=a.project)?void 0:e.status)})).length),F.value=l.length>=10}else console.error("❌ API调用失败或返回数据格式错误:",e),a||(M.value=[]),F.value=!1}catch(e){console.error("加载支持列表失败:",e),s({title:"加载失败",icon:"none"})}finally{C.value=!1}}},G=()=>{I.value++,B(!0)},H=a=>["待支付","已支付","已退款"][a]||"未知",Q=a=>["审核中","进行中","已成功","已失败"][a]||"未知",R=a=>{const e=new Date(1e3*a),l=new Date-e;return l<6e4?"刚刚":l<36e5?Math.floor(l/6e4)+"分钟前":l<864e5?Math.floor(l/36e5)+"小时前":l<2592e6?Math.floor(l/864e5)+"天前":e.toLocaleDateString()},T=()=>{g({url:"/addon/niucrowd/pages/index"})};return(a,e)=>{const l=r,s=m,g=y,h=w;return t(),u(l,{class:"my-supports-page"},{default:o((()=>[c(" 状态筛选 "),n(l,{class:"filter-tabs"},{default:o((()=>[(t(),d(p,null,i(x,((a,e)=>n(l,{key:e,class:b(["tab-item",{active:A.value===a.value}]),onClick:e=>{return l=a.value,A.value=l,void B();var l}},{default:o((()=>[v(f(a.label),1)])),_:2},1032,["class","onClick"]))),64))])),_:1}),c(" 支持列表 "),n(l,{class:"support-list"},{default:o((()=>[C.value?(t(),u(l,{key:0,class:"loading-wrapper"},{default:o((()=>[n(s,{class:"loading-text"},{default:o((()=>[v("加载中...")])),_:1})])),_:1})):0===M.value.length?(t(),u(l,{key:1,class:"empty-wrapper"},{default:o((()=>[n(l,{class:"empty-icon"},{default:o((()=>[n(s,{class:"iconfont iconwushuju"})])),_:1}),n(s,{class:"empty-text"},{default:o((()=>[v("暂无支持记录")])),_:1}),n(g,{class:"browse-btn",onClick:T},{default:o((()=>[v("去逛逛")])),_:1})])),_:1})):(t(),u(l,{key:2},{default:o((()=>[(t(!0),d(p,null,i(M.value,(a=>(t(),u(l,{key:a.support_id,class:"support-item",onClick:e=>{return l=a.project_id,void j({url:`/addon/niucrowd/pages/project/detail?id=${l}`});var l}},{default:o((()=>[n(l,{class:"support-image"},{default:o((()=>{var e;return[n(h,{src:(null==(e=a.project)?void 0:e.cover_image)||S.value,mode:"aspectFill",class:"cover-img"},null,8,["src"])]})),_:2},1024),n(l,{class:"support-info"},{default:o((()=>[n(l,{class:"project-name"},{default:o((()=>{var e;return[v(f(null==(e=a.project)?void 0:e.project_name),1)]})),_:2},1024),n(l,{class:"support-amount"},{default:o((()=>[v("支持金额：¥"+f(a.amount),1)])),_:2},1024),n(l,{class:"support-time"},{default:o((()=>[v(f(R(a.create_time)),1)])),_:2},1024),a.reward?(t(),u(l,{key:0,class:"reward-info"},{default:o((()=>[n(s,{class:"reward-label"},{default:o((()=>[v("回报：")])),_:1}),n(s,{class:"reward-content"},{default:o((()=>[v(f(a.reward.content),1)])),_:2},1024)])),_:2},1024)):c("v-if",!0)])),_:2},1024),n(l,{class:"support-status"},{default:o((()=>{return[n(l,{class:b(["status-badge",(e=a.pay_status,["pending","success","warning"][e]||"pending")])},{default:o((()=>[v(f(H(a.pay_status)),1)])),_:2},1032,["class"]),n(l,{class:"project-status"},{default:o((()=>{var e;return[v(" 项目"+f(Q(null==(e=a.project)?void 0:e.status)),1)]})),_:2},1024)];var e})),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1}))])),_:1}),c(" 加载更多 "),F.value&&!C.value?(t(),u(l,{key:0,class:"load-more",onClick:G},{default:o((()=>[n(s,null,{default:o((()=>[v("加载更多")])),_:1})])),_:1})):c("v-if",!0),c(" 统计信息 "),n(l,{class:"stats-section"},{default:o((()=>[n(l,{class:"stats-card"},{default:o((()=>[n(l,{class:"stats-item"},{default:o((()=>[n(l,{class:"stats-value"},{default:o((()=>[v(f(_(D)),1)])),_:1}),n(l,{class:"stats-label"},{default:o((()=>[v("支持项目")])),_:1})])),_:1}),n(l,{class:"stats-item"},{default:o((()=>[n(l,{class:"stats-value"},{default:o((()=>[v("¥"+f(_(P)),1)])),_:1}),n(l,{class:"stats-label"},{default:o((()=>[v("支持金额")])),_:1})])),_:1}),n(l,{class:"stats-item"},{default:o((()=>[n(l,{class:"stats-value"},{default:o((()=>[v(f(_(L)),1)])),_:1}),n(l,{class:"stats-label"},{default:o((()=>[v("成功项目")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})}}},[["__scopeId","data-v-0a45128b"]]);export{x as default};
