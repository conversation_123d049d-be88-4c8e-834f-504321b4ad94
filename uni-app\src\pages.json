{
    "pages": [
        {
            "path": "app/pages/index/index",
            "style": {
                "navigationStyle": "custom",
                "navigationBarTitleText": "%pages.index.index%",
                "usingComponents": {
                    "diy-group": "../../../../addon/components/diy/group/index"
                },
                "componentPlaceholder": {
                    "diy-group": "view"
                }
            }
        },
        {
            "path": "app/pages/auth/index",
            "style": {
                
                "navigationStyle": "custom",
                
                "navigationBarTitleText": "%pages.auth.index%"
            }
        },
        {
            "path": "app/pages/auth/agreement",
            "style": {
                "navigationBarTitleText": "%pages.auth.agreement%"
            }
        },
        {
            "path": "app/pages/auth/bind",
            "style": {
                "navigationStyle": "custom",
                "navigationBarTitleText": "%pages.auth.bind%"
            }
        },
        {
            "path": "app/pages/auth/login",
            "style": {
                "navigationStyle": "custom",
                "navigationBarTitleText": "%pages.auth.login%"
            }
        },
        {
            "path": "app/pages/auth/register",
            "style": {
                "navigationStyle": "custom",
                "navigationBarTitleText": "%pages.auth.register%"
            }
        },
        {
            "path": "app/pages/auth/resetpwd",
            "style": {
              
                "navigationStyle": "custom",
                
                "navigationBarTitleText": "%pages.auth.resetpwd%"
            }
        },
        {
            "path": "app/pages/index/diy",
            "style": {
               
                "navigationStyle": "custom",
               
                "navigationBarTitleText": "%pages.index.diy%",
                "usingComponents": {
                    "diy-group": "../../../../addon/components/diy/group/index"
                },
                "componentPlaceholder": {
                    "diy-group": "view"
                }
            }
        },
        {
            "path": "app/pages/index/diy_form",
            "style": {
              
                "navigationStyle": "custom",
       
                "navigationBarTitleText": "%pages.index.diy_form%",
                "usingComponents": {
                    "diy-group": "../../../../addon/components/diy/group/index"
                },
                "componentPlaceholder": {
                    "diy-group": "view"
                }
            }
        },
        {
            "path": "app/pages/index/diy_form_result",
            "style": {
              
                "navigationStyle": "custom",
               
                "navigationBarTitleText": "%pages.index.diy_form_result%"
            },
            "needLogin": true
        },
        {
            "path": "app/pages/index/diy_form_detail",
            "style": {
                "navigationBarTitleText": "%pages.index.diy_form_detail%"
            },
            "needLogin": true
        },
        {
            "path": "app/pages/index/close",
            "style": {
                "navigationBarTitleText": "%pages.index.close%"
            }
        },
        {
            "path": "app/pages/index/nosite",
            "style": {
                "navigationBarTitleText": "%pages.index.nosite%"
            }
        },
        {
            "path": "app/pages/pay/browser",
            "style": {
                "navigationBarTitleText": "%pages.pay.browser%"
            }
        },
        {
            "path": "app/pages/pay/result",
            "style": {
              
                "navigationStyle": "custom",
             
                "navigationBarTitleText": "%pages.pay.result%"
            }
        },
        {
            "path": "app/pages/setting/index",
            "style": {
                "navigationBarTitleText": "%pages.setting.index%"
            },
            "needLogin": true
        },
        {
            "path": "app/pages/webview/index",
            "style": {
                "navigationBarTitleText": "%pages.webview.index%"
            }
        },
        {
            "path": "app/pages/index/develop",
            "style": {
                "navigationBarTitleText": "%pages.index.develop%"
            }
        },
        {
            "path": "app/pages/verify/index",
            "style": {
                "navigationBarTitleText": "%pages.verify.index%"
            },
            "needLogin": true
        },
        {
            "path": "app/pages/verify/verify",
            "style": {
                "navigationBarTitleText": "%pages.verify.verify%"
            },
            "needLogin": true
        },
        {
            "path": "app/pages/verify/detail",
            "style": {
                "navigationBarTitleText": "%pages.verify.detail%"
            },
            "needLogin": true
        },
        {
            "path": "app/pages/verify/record",
            "style": {
                "navigationBarTitleText": "%pages.verify.record%"
            },
            "needLogin": true
        },
        {
            "path": "app/pages/weapp/order_shipping",
            "style": {
                "navigationBarTitleText": "%pages.weapp.order_shipping%"
            }
        },
        {
            "path": "app/pages/friendspay/share",
            "style": {
               
                "navigationStyle": "custom",
               
                "navigationBarTitleText": "%pages.friendspay.share%"
            },
            "needLogin": true
        },
        {
            "path": "app/pages/friendspay/money",
            "style": {
                "navigationStyle": "custom",
                "navigationBarTitleText": "%pages.friendspay.money%"
            }
        }
    ],
    "subPackages": [
   
        {
            "root": "addon/shop",
            "pages": [
               {
                    "path": "pages/index",
                    "style": {
                       
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%shop.pages.index%"
                    }
                },
                {
                    "path": "pages/coupon/list",
                    "style": {
                       
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%shop.pages.coupon.list%"
                    }
                },
                {
                    "path": "pages/coupon/detail",
                    "style": {
                       
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%shop.pages.coupon.detail%"
                    }
                },
                {
                    "path": "pages/discount/list",
                    "style": {
                        
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%shop.pages.discount.list%"
                    }
                },
                {
                    "path": "pages/evaluate/list",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.evaluate.list%"
                    }
                },
                {
                    "path": "pages/evaluate/order_evaluate",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.evaluate.order_evaluate%"
                    }
                },
                {
                    "path": "pages/evaluate/order_evaluate_view",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.evaluate.order_evaluate_view%"
                    }
                },
                {
                    "path": "pages/member/my_coupon",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.member.my_coupon%"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/member/index",
                    "style": {
                       
                        "navigationStyle": "custom",
                        
                        "navigationBarTitleText": "%shop.pages.member.index%"
                    }
                },
                {
                    "path": "pages/goods/search",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.goods.search%"
                    }
                },
                {
                    "path": "pages/goods/list",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.goods.list%"
                    }
                },
                {
                    "path": "pages/goods/rank",
                    "style": {
                       
                        "navigationStyle": "custom",
                  
                        "navigationBarTitleText": "%shop.pages.goods.rank%"
                    }
                },
                {
                    "path": "pages/newcomer/list",
                    "style": {
                      
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%shop.pages.newcomer.list%"
                    }
                },
                {
                    "path": "pages/goods/detail",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.goods.detail%",
                        "navigationStyle": "custom"
                    }
                },
                {
                    "path": "pages/goods/cart",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.goods.cart%"
                    }
                },
                {
                    "path": "pages/goods/collect",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.goods.collect%"
                    },
					"needLogin": true
                },
                {
                    "path": "pages/goods/browse",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.goods.browse%"
                    },
					"needLogin": true
                },
                {
                    "path": "pages/goods/category",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.goods.category%"
                    }
                },
                {
                    "path": "pages/order/detail",
                    "style": {
                       
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%shop.pages.order.detail%"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/order/list",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.order.list%"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/order/payment",
                    "style": {
                      
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%shop.pages.order.payment%"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/refund/apply",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.refund.apply%"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/refund/edit_apply",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.refund.edit_apply%"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/refund/list",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.refund.list%"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/refund/detail",
                    "style": {
                      
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%shop.pages.refund.detail%"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/refund/log",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.refund.log%"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/point/index",
                    "style": {
                      
                        "navigationStyle": "custom",
                      
                        "navigationBarTitleText": "%shop.pages.point.index%"
                    }
                },
                {
                    "path": "pages/point/list",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.point.list%"
                    }
                },
                {
                    "path": "pages/point/detail",
                    "style": {
                        "navigationStyle": "custom",
                        "navigationBarTitleText": "%shop.pages.point.detail%"
                    }
                },
                {
                    "path": "pages/point/payment",
                    "style": {
                       
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%shop.pages.point.payment%"
                    }
                },
                {
                    "path": "pages/point/order_list",
                    "style": {
                        "navigationBarTitleText": "%shop.pages.point.order_list%"
                    }
                }
            ]
        },
      
     
        {
            "root": "addon/niucrowd",
            "pages": [
                 {
                    "path": "pages/index",
                    "style": {
                      
                        "navigationStyle": "custom",
                      
                        "navigationBarTitleText": "%niucrowd.pages.index%"
                    }
                },
                {
                    "path": "pages/project/list",
                    "style": {
                        "navigationBarTitleText": "%niucrowd.pages.project.list%"
                    }
                },
                {
                    "path": "pages/project/detail",
                    "style": {
                        "navigationBarTitleText": "%niucrowd.pages.project.detail%"
                    }
                },
                {
                    "path": "pages/project/search",
                    "style": {
                        "navigationBarTitleText": "%niucrowd.pages.project.search%"
                    }
                },
                {
                    "path": "pages/project/create",
                    "style": {
                        "navigationBarTitleText": "%niucrowd.pages.project.create%"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/member/index",
                    "style": {
                        "navigationBarTitleText": "%niucrowd.pages.member.index%",
                        "navigationStyle": "custom"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/member/projects",
                    "style": {
                        "navigationBarTitleText": "%niucrowd.pages.member.projects%"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/member/supports",
                    "style": {
                        "navigationBarTitleText": "%niucrowd.pages.member.supports%"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/member/auth",
                    "style": {
                        "navigationBarTitleText": "%niucrowd.pages.member.auth%"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/member/auth-status",
                    "style": {
                        "navigationBarTitleText": "%niucrowd.pages.member.auth_status%"
                    },
                    "needLogin": true
                },
                {
                    "path": "pages/member/auth-router",
                    "style": {
                        "navigationBarTitleText": "身份认证跳转",
                        "backgroundColor": "#f8f9fa"
                    }
                },
                {
                    "path": "pages/member/update-publish",
                    "style": {
                        "navigationBarTitleText": "发布项目动态"
                    },
                    "needLogin": true
                }
            ]
        },
      
        {
            "root": "app/components",
            "pages": []
        },
        {
            "root": "app/pages/member",
            "pages": [
                {
                    "path": "apply_cash_out",
                    "style": {
                        "navigationBarTitleText": "%pages.member.apply_cash_out%"
                    },
                    "needLogin": true
                },
                {
                    "path": "commission",
                    "style": {
                        
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%pages.member.commission%"
                    },
                    "needLogin": true
                },
                {
                    "path": "balance",
                    "style": {
                       
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%pages.member.balance%"
                    },
                    "needLogin": true
                },
                {
                    "path": "level",
                    "style": {
                      
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%pages.member.level%"
                    },
                    "needLogin": true
                },
                {
                    "path": "detailed_account",
                    "style": {
                        "navigationBarTitleText": "%pages.member.detailed_account%"
                    }
                },
                {
                    "path": "cash_out",
                    "style": {
                        "navigationBarTitleText": "%pages.member.cash_out%"
                    }
                },
                {
                    "path": "cash_out_detail",
                    "style": {
                        "navigationBarTitleText": "%pages.member.cash_out_detail%"
                    }
                },
                {
                    "path": "index",
                    "style": {
                       
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%pages.member.index%",
                        "usingComponents": {
                            "diy-group": "../../../../addon/components/diy/group/index"
                        },
                        "componentPlaceholder": {
                            "diy-group": "view"
                        }
                    }
                },
                {
                    "path": "personal",
                    "style": {
                        "navigationBarTitleText": "%pages.member.personal%"
                    },
                    "needLogin": true
                },
                {
                    "path": "personal_form",
                    "style": {
                     
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%pages.member.personal_form%"
                    },
                    "needLogin": true
                },
                {
                    "path": "point",
                    "style": {
                     
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%pages.member.point%"
                    },
                    "needLogin": true
                },
                {
                    "path": "point_detail",
                    "style": {
                        "navigationBarTitleText": "%pages.member.point_detail%"
                    },
                    "needLogin": true
                },
                {
                    "path": "account",
                    "style": {
                        "navigationBarTitleText": "%pages.member.account%"
                    },
                    "needLogin": true
                },
                {
                    "path": "account_edit",
                    "style": {
                        "navigationBarTitleText": "%pages.member.account_edit%"
                    },
                    "needLogin": true
                },
                {
                    "path": "address",
                    "style": {
                        "navigationBarTitleText": "%pages.member.address%"
                    },
                    "needLogin": true
                },
                {
                    "path": "address_edit",
                    "style": {
                        "navigationBarTitleText": "%pages.member.address_edit%"
                    },
                    "needLogin": true
                },
                {
                    "path": "sign_in",
                    "style": {
                       
                        "navigationStyle": "custom",
                       
                        "navigationBarTitleText": "%pages.member.sign_in%"
                    },
                    "needLogin": true
                },
                {
                    "path": "contact",
                    "style": {
                        "navigationBarTitleText": "%pages.member.contact%"
                    }
                }
            ]
        }
    ],
    "globalStyle": {
        "navigationBarTextStyle": "black",
        "navigationBarTitleText": "",
        "navigationBarBackgroundColor": "#ffffff",
        "backgroundColor": "#F6F6F6",
        "backgroundColorTop": "#F6F6F6",
        "backgroundColorBottom": "#F6F6F6"
    },
    "tabBar": {
        "list": [{
                "pagePath": "app/pages/index/index"
            },
            {
                "pagePath": "app/pages/index/nosite"
            }
        ]
    },
    "uniIdRouter": {},
    "easycom": {
        "custom": {
            "diy-group": "@/addon/components/diy/group/index.vue",
            "^u-(.*)": "uview-plus/components/u-$1/u-$1.vue",
            "^up-(.*)": "uview-plus/components/u-$1/u-$1.vue",
            "^u-([^-].*)": "uview-plus/components/u-$1/u-$1.vue",
            "diy-(\W.*)": "@/app/components/diy/$1/index.vue"
        }
    }
}
