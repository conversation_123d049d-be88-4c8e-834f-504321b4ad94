import{a4 as e,a5 as t,a6 as a,o as s,c as n,w as i,g as c,k as o,r,F as l,l as u,bw as h,bx as d,d as m,N as p,i as g,j as f,R as x,b as v,A as y,B as T,e as _,T as C,S as b,y as S,G as w}from"./index-12d8dd28.js";import{_ as k}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as N}from"./u-input.52919f31.js";import{_ as E}from"./u-modal.603e6547.js";const G=k({name:"u-code",mixins:[t,a,{props:{seconds:{type:[String,Number],default:()=>e.code.seconds},startText:{type:String,default:()=>e.code.startText},changeText:{type:String,default:()=>e.code.changeText},endText:{type:String,default:()=>e.code.endText},keepRunning:{type:Boolean,default:()=>e.code.keepRunning},uniqueKey:{type:String,default:()=>e.code.uniqueKey}}}],data(){return{secNum:this.seconds,timer:null,canGetCode:!0}},mounted(){this.checkKeepRunning()},watch:{seconds:{immediate:!0,handler(e){this.secNum=e}}},emits:["start","end","change"],methods:{checkKeepRunning(){let e=Number(uni.getStorageSync(this.uniqueKey+"_$uCountDownTimestamp"));if(!e)return this.changeEvent(this.startText);let t=Math.floor(+new Date/1e3);this.keepRunning&&e&&e>t?(this.secNum=e-t,uni.removeStorageSync(this.uniqueKey+"_$uCountDownTimestamp"),this.start()):this.changeEvent(this.startText)},start(){this.timer&&(clearInterval(this.timer),this.timer=null),this.$emit("start"),this.canGetCode=!1,this.changeEvent(this.changeText.replace(/x|X/,this.secNum)),this.timer=setInterval((()=>{--this.secNum?this.changeEvent(this.changeText.replace(/x|X/,this.secNum)):(clearInterval(this.timer),this.timer=null,this.changeEvent(this.endText),this.secNum=this.seconds,this.$emit("end"),this.canGetCode=!0)}),1e3),this.setTimeToStorage()},reset(){this.canGetCode=!0,clearInterval(this.timer),this.secNum=this.seconds,this.changeEvent(this.endText)},changeEvent(e){this.$emit("change",e)},setTimeToStorage(){if(this.keepRunning&&this.timer&&this.secNum>0&&this.secNum<this.seconds){let e=Math.floor(+new Date/1e3);uni.setStorage({key:this.uniqueKey+"_$uCountDownTimestamp",data:e+Number(this.secNum)})}}},beforeUnmount(){this.setTimeToStorage(),clearTimeout(this.timer),this.timer=null}},[["render",function(e,t,a,r,l,u){const h=o;return s(),n(h,{class:"u-code"},{default:i((()=>[c(" 此组件功能由js完成，无需写html逻辑 ")])),_:1})}],["__scopeId","data-v-75751f8d"]]);function $(e){const t=r("");return{image:t,refresh:async()=>{try{const a=await d();e.captcha_key=a.data.captcha_key,e.captcha_code="",t.value=a.data.img.replace(/\r\n/g,"")}catch(a){}}}}const R=m({__name:"sms-code",props:{mobile:String,type:String,isAgree:{type:Boolean,default:!0},modelValue:{type:String,default:""}},emits:["update:modelValue"],setup(e,{emit:t}){const a=e,n=u({get:()=>a.modelValue,set(e){t("update:modelValue",e)}}),c=r(null),d=function(e){const t=r(l("getSmsCode")),a="X"+l("smsCodeChangeText"),s=u((()=>!e.value||e.value.canGetCode));return{tips:t,seconds:60,canGetCode:s,send:async t=>{if(!s.value)return;e.value.start();let a=!1;return await h(t).then((t=>{1==t.code?a=t.data.key:(e.value.reset(),a=!1)})).catch((t=>{a=!1,e.value.reset()})),a},codeChange:e=>{t.value=e},changeText:a}}(c),m=r(!1),k=p({mobile:"",captcha_code:"",captcha_key:"",type:a.type}),R=$(k),j=async()=>{if(c.value.canGetCode){if(k.mobile=a.mobile,!a.isAgree)return void S({title:l("isAgreeTips"),icon:"none"});if(uni.$u.test.isEmpty(k.mobile))return void S({title:l("mobilePlaceholder"),icon:"none"});if(!uni.$u.test.mobile(k.mobile))return void S({title:l("mobileError"),icon:"none"});await R.refresh(),m.value=!0}},K=async()=>{if(uni.$u.test.isEmpty(k.captcha_code))return void S({title:l("captchaPlaceholder"),icon:"none"});const e=await d.send(k);e?(n.value=e,m.value=!1):!1===e&&R.refresh()};return(e,t)=>{const a=o,n=g(f("u-code"),G),r=g(f("u-input"),N),u=w,h=g(f("u-modal"),E);return s(),x(b,null,[v(a,{class:C(["text-[26rpx]",{"text-primary":_(d).canGetCode.value,"text-gray-300":!_(d).canGetCode.value}]),onClick:j},{default:i((()=>[y(T(_(d).tips.value),1)])),_:1},8,["class"]),v(n,{seconds:_(d).seconds,"change-text":_(d).changeText,ref_key:"smsRef",ref:c,onChange:_(d).codeChange},null,8,["seconds","change-text","onChange"]),v(h,{show:m.value,title:_(l)("captchaTitle"),"confirm-text":_(l)("confirm"),"cancel-text":_(l)("cancel"),"show-cancel-button":!0,onCancel:t[2]||(t[2]=e=>m.value=!1),onConfirm:K,confirmColor:"var(--primary-color)"},{default:i((()=>[v(a,{class:"flex mt-[20rpx]"},{default:i((()=>[v(r,{placeholder:_(l)("captchaPlaceholder"),border:"surround",modelValue:k.captcha_code,"onUpdate:modelValue":t[0]||(t[0]=e=>k.captcha_code=e)},null,8,["placeholder","modelValue"]),v(u,{src:_(R).image.value,class:"h-[76rpx] w-[auto] ml-[20rpx]",mode:"heightFix",onClick:t[1]||(t[1]=e=>_(R).refresh())},null,8,["src"])])),_:1})])),_:1},8,["show","title","confirm-text","cancel-text"])],64)}}});export{R as _,$ as u};
